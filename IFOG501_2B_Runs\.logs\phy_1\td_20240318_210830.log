============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Mon Mar 18 21:08:30 2024

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 16 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1602 instances
RUN-0007 : 385 luts, 968 seqs, 125 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2134 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1613 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     233     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     287     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1600 instances, 385 luts, 968 seqs, 200 slices, 23 macros(200 instances: 125 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7612, tnet num: 2132, tinst num: 1600, tnode num: 10738, tedge num: 12821.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.187762s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (33.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 583510
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1600.
PHY-3001 : End clustering;  0.000007s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 486307, overlap = 18
PHY-3002 : Step(2): len = 450963, overlap = 20.25
PHY-3002 : Step(3): len = 388122, overlap = 13.5
PHY-3002 : Step(4): len = 345474, overlap = 11.25
PHY-3002 : Step(5): len = 323190, overlap = 18
PHY-3002 : Step(6): len = 315263, overlap = 18
PHY-3002 : Step(7): len = 307547, overlap = 20.25
PHY-3002 : Step(8): len = 301254, overlap = 20.25
PHY-3002 : Step(9): len = 288543, overlap = 20.25
PHY-3002 : Step(10): len = 281180, overlap = 20.25
PHY-3002 : Step(11): len = 275427, overlap = 20.25
PHY-3002 : Step(12): len = 268922, overlap = 20.25
PHY-3002 : Step(13): len = 261013, overlap = 20.25
PHY-3002 : Step(14): len = 256380, overlap = 20.25
PHY-3002 : Step(15): len = 250942, overlap = 20.25
PHY-3002 : Step(16): len = 244942, overlap = 20.25
PHY-3002 : Step(17): len = 238944, overlap = 20.25
PHY-3002 : Step(18): len = 235362, overlap = 20.25
PHY-3002 : Step(19): len = 229274, overlap = 20.25
PHY-3002 : Step(20): len = 225236, overlap = 20.25
PHY-3002 : Step(21): len = 219843, overlap = 20.25
PHY-3002 : Step(22): len = 216000, overlap = 20.25
PHY-3002 : Step(23): len = 210260, overlap = 20.25
PHY-3002 : Step(24): len = 206450, overlap = 20.25
PHY-3002 : Step(25): len = 201806, overlap = 20.25
PHY-3002 : Step(26): len = 198326, overlap = 20.25
PHY-3002 : Step(27): len = 192803, overlap = 20.25
PHY-3002 : Step(28): len = 186650, overlap = 20.25
PHY-3002 : Step(29): len = 182109, overlap = 20.25
PHY-3002 : Step(30): len = 179950, overlap = 20.25
PHY-3002 : Step(31): len = 169600, overlap = 20.25
PHY-3002 : Step(32): len = 158421, overlap = 20.25
PHY-3002 : Step(33): len = 155533, overlap = 20.25
PHY-3002 : Step(34): len = 153271, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000139496
PHY-3002 : Step(35): len = 154744, overlap = 13.5
PHY-3002 : Step(36): len = 154117, overlap = 11.25
PHY-3002 : Step(37): len = 150748, overlap = 15.75
PHY-3002 : Step(38): len = 148542, overlap = 18
PHY-3002 : Step(39): len = 146788, overlap = 11.25
PHY-3002 : Step(40): len = 141067, overlap = 6.75
PHY-3002 : Step(41): len = 136120, overlap = 11.25
PHY-3002 : Step(42): len = 134511, overlap = 9
PHY-3002 : Step(43): len = 132820, overlap = 11.25
PHY-3002 : Step(44): len = 126633, overlap = 15.75
PHY-3002 : Step(45): len = 121053, overlap = 11.25
PHY-3002 : Step(46): len = 118646, overlap = 9
PHY-3002 : Step(47): len = 117909, overlap = 6.75
PHY-3002 : Step(48): len = 114716, overlap = 9
PHY-3002 : Step(49): len = 113787, overlap = 6.75
PHY-3002 : Step(50): len = 110246, overlap = 4.5
PHY-3002 : Step(51): len = 109233, overlap = 9
PHY-3002 : Step(52): len = 107638, overlap = 11.25
PHY-3002 : Step(53): len = 107099, overlap = 9
PHY-3002 : Step(54): len = 105266, overlap = 6.75
PHY-3002 : Step(55): len = 103566, overlap = 9
PHY-3002 : Step(56): len = 101086, overlap = 6.75
PHY-3002 : Step(57): len = 100474, overlap = 6.75
PHY-3002 : Step(58): len = 99175.8, overlap = 6.8125
PHY-3002 : Step(59): len = 97696, overlap = 9.3125
PHY-3002 : Step(60): len = 95828.9, overlap = 9.3125
PHY-3002 : Step(61): len = 94607.1, overlap = 9.4375
PHY-3002 : Step(62): len = 93027.4, overlap = 7.25
PHY-3002 : Step(63): len = 91948.2, overlap = 7.125
PHY-3002 : Step(64): len = 90688.9, overlap = 7.0625
PHY-3002 : Step(65): len = 89692.8, overlap = 9.25
PHY-3002 : Step(66): len = 87144.1, overlap = 7.375
PHY-3002 : Step(67): len = 85845.1, overlap = 7.875
PHY-3002 : Step(68): len = 84239.4, overlap = 8.125
PHY-3002 : Step(69): len = 84060.9, overlap = 8.25
PHY-3002 : Step(70): len = 76969, overlap = 11
PHY-3002 : Step(71): len = 75542, overlap = 13.25
PHY-3002 : Step(72): len = 74297.1, overlap = 11
PHY-3002 : Step(73): len = 73200.8, overlap = 8.4375
PHY-3002 : Step(74): len = 72243.2, overlap = 13
PHY-3002 : Step(75): len = 71407.7, overlap = 15.25
PHY-3002 : Step(76): len = 71051.8, overlap = 15.125
PHY-3002 : Step(77): len = 70639.6, overlap = 10.625
PHY-3002 : Step(78): len = 69557.6, overlap = 10.5
PHY-3002 : Step(79): len = 69335.7, overlap = 10.5
PHY-3002 : Step(80): len = 68406.7, overlap = 10.4375
PHY-3002 : Step(81): len = 65426.7, overlap = 12.125
PHY-3002 : Step(82): len = 64482.2, overlap = 12.125
PHY-3002 : Step(83): len = 63568.6, overlap = 5.3125
PHY-3002 : Step(84): len = 63247.7, overlap = 7.5625
PHY-3002 : Step(85): len = 63182.5, overlap = 7.5625
PHY-3002 : Step(86): len = 62993.4, overlap = 9.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000278992
PHY-3002 : Step(87): len = 63006.2, overlap = 7.5625
PHY-3002 : Step(88): len = 63039.1, overlap = 7.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000557985
PHY-3002 : Step(89): len = 62961.8, overlap = 7.375
PHY-3002 : Step(90): len = 62884.3, overlap = 7.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004240s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038388s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(91): len = 64688.2, overlap = 10.0312
PHY-3002 : Step(92): len = 63514.8, overlap = 10.4062
PHY-3002 : Step(93): len = 62381.1, overlap = 10.5312
PHY-3002 : Step(94): len = 61289, overlap = 12.0938
PHY-3002 : Step(95): len = 59740.2, overlap = 14.3125
PHY-3002 : Step(96): len = 58386.4, overlap = 14.7812
PHY-3002 : Step(97): len = 57306.8, overlap = 13.125
PHY-3002 : Step(98): len = 56424.7, overlap = 10.4062
PHY-3002 : Step(99): len = 54645.3, overlap = 8.0625
PHY-3002 : Step(100): len = 53396.1, overlap = 8
PHY-3002 : Step(101): len = 52708.5, overlap = 7.84375
PHY-3002 : Step(102): len = 51680.9, overlap = 7.25
PHY-3002 : Step(103): len = 50563.1, overlap = 6.3125
PHY-3002 : Step(104): len = 49960.6, overlap = 5.5625
PHY-3002 : Step(105): len = 49252.4, overlap = 4.59375
PHY-3002 : Step(106): len = 48510.8, overlap = 3.25
PHY-3002 : Step(107): len = 47867.2, overlap = 2.375
PHY-3002 : Step(108): len = 46921.7, overlap = 5.0625
PHY-3002 : Step(109): len = 45973.5, overlap = 15.3125
PHY-3002 : Step(110): len = 45408.1, overlap = 16.5
PHY-3002 : Step(111): len = 44954.4, overlap = 17.0625
PHY-3002 : Step(112): len = 44738.4, overlap = 17.3125
PHY-3002 : Step(113): len = 44548.7, overlap = 17.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000221615
PHY-3002 : Step(114): len = 44372.5, overlap = 17.3438
PHY-3002 : Step(115): len = 44281.8, overlap = 16.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000443229
PHY-3002 : Step(116): len = 44244.8, overlap = 16.9375
PHY-3002 : Step(117): len = 44311.2, overlap = 16.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.044226s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (106.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.24055e-05
PHY-3002 : Step(118): len = 44396.3, overlap = 69.2188
PHY-3002 : Step(119): len = 45145.2, overlap = 71.2188
PHY-3002 : Step(120): len = 45677.9, overlap = 69.9375
PHY-3002 : Step(121): len = 45588, overlap = 62.7812
PHY-3002 : Step(122): len = 45720.8, overlap = 63.125
PHY-3002 : Step(123): len = 45815.1, overlap = 63.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000124811
PHY-3002 : Step(124): len = 46263.9, overlap = 62.375
PHY-3002 : Step(125): len = 46543.7, overlap = 57.3438
PHY-3002 : Step(126): len = 47241.6, overlap = 54.7812
PHY-3002 : Step(127): len = 47660.7, overlap = 53.5312
PHY-3002 : Step(128): len = 47937.8, overlap = 51.3125
PHY-3002 : Step(129): len = 48270.8, overlap = 51.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000249622
PHY-3002 : Step(130): len = 48267.2, overlap = 54.0625
PHY-3002 : Step(131): len = 48734.4, overlap = 54.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000499244
PHY-3002 : Step(132): len = 49491.6, overlap = 46.0938
PHY-3002 : Step(133): len = 50721.1, overlap = 43.5312
PHY-3002 : Step(134): len = 51636.3, overlap = 39.6875
PHY-3002 : Step(135): len = 51134.6, overlap = 38.875
PHY-3002 : Step(136): len = 50626.3, overlap = 38.9688
PHY-3002 : Step(137): len = 50534.2, overlap = 39.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000998488
PHY-3002 : Step(138): len = 50941, overlap = 39.2188
PHY-3002 : Step(139): len = 51037.1, overlap = 38.7188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00182477
PHY-3002 : Step(140): len = 51618.7, overlap = 37.0938
PHY-3002 : Step(141): len = 51534.1, overlap = 36.0625
PHY-3002 : Step(142): len = 51623.4, overlap = 32.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7612, tnet num: 2132, tinst num: 1600, tnode num: 10738, tedge num: 12821.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 83.31 peak overflow 2.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2134.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 54776, over cnt = 236(0%), over = 952, worst = 18
PHY-1001 : End global iterations;  0.057197s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.0%)

PHY-1001 : Congestion index: top1 = 41.92, top5 = 24.93, top10 = 16.33, top15 = 11.62.
PHY-1001 : End incremental global routing;  0.091505s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (51.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2132 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043658s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (35.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1561 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1602 instances, 385 luts, 970 seqs, 200 slices, 23 macros(200 instances: 125 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 51828.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7620, tnet num: 2134, tinst num: 1602, tnode num: 10752, tedge num: 12833.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2134 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.202334s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (69.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(143): len = 51849.8, overlap = 2.75
PHY-3002 : Step(144): len = 51826.4, overlap = 2.75
PHY-3002 : Step(145): len = 51813, overlap = 2.75
PHY-3002 : Step(146): len = 51813, overlap = 2.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2134 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037834s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (82.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00131156
PHY-3002 : Step(147): len = 51839.4, overlap = 32.1875
PHY-3002 : Step(148): len = 51839.4, overlap = 32.1875
PHY-3001 : Final: Len = 51839.4, Over = 32.1875
PHY-3001 : End incremental placement;  0.313915s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (64.7%)

OPT-1001 : Total overflow 83.31 peak overflow 2.00
OPT-1001 : End high-fanout net optimization;  0.471589s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (59.6%)

OPT-1001 : Current memory(MB): used = 218, reserve = 190, peak = 218.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1631/2136.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 54920, over cnt = 236(0%), over = 951, worst = 18
PHY-1002 : len = 60400, over cnt = 155(0%), over = 350, worst = 12
PHY-1002 : len = 62504, over cnt = 99(0%), over = 192, worst = 8
PHY-1002 : len = 64544, over cnt = 24(0%), over = 30, worst = 4
PHY-1002 : len = 64896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.059377s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 36.57, top5 = 25.05, top10 = 18.00, top15 = 13.36.
OPT-1001 : End congestion update;  0.090888s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (34.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2134 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.036867s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.129810s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (48.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 187, peak = 218.
OPT-1001 : End physical optimization;  0.786395s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (57.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 385 LUT to BLE ...
SYN-4008 : Packed 385 LUT and 183 SEQ to BLE.
SYN-4003 : Packing 787 remaining SEQ's ...
SYN-4005 : Packed 95 SEQ with LUT/SLICE
SYN-4006 : 125 single LUT's are left
SYN-4006 : 692 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1077/1384 primitive instances ...
PHY-3001 : End packing;  0.037527s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (41.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 818 instances
RUN-1001 : 385 mslices, 384 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1968 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1447 nets have 2 pins
RUN-1001 : 400 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 816 instances, 769 slices, 23 macros(200 instances: 125 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51595.8, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6398, tnet num: 1966, tinst num: 816, tnode num: 8639, tedge num: 11203.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.205720s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (68.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.27483e-05
PHY-3002 : Step(149): len = 51006, overlap = 57.75
PHY-3002 : Step(150): len = 50431.2, overlap = 56.75
PHY-3002 : Step(151): len = 49957.3, overlap = 55.5
PHY-3002 : Step(152): len = 49533, overlap = 56.75
PHY-3002 : Step(153): len = 49244.5, overlap = 56.5
PHY-3002 : Step(154): len = 49133.1, overlap = 56.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.54966e-05
PHY-3002 : Step(155): len = 49400.6, overlap = 55.75
PHY-3002 : Step(156): len = 49908.3, overlap = 55
PHY-3002 : Step(157): len = 50356.6, overlap = 54.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000130993
PHY-3002 : Step(158): len = 51324.2, overlap = 51.25
PHY-3002 : Step(159): len = 52103.9, overlap = 51
PHY-3002 : Step(160): len = 52344.4, overlap = 49.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.071452s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (21.9%)

PHY-3001 : Trial Legalized: Len = 65590.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.032082s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (48.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000678984
PHY-3002 : Step(161): len = 63468.8, overlap = 5.75
PHY-3002 : Step(162): len = 61045.2, overlap = 9.75
PHY-3002 : Step(163): len = 58952.3, overlap = 12.75
PHY-3002 : Step(164): len = 57696.5, overlap = 16.5
PHY-3002 : Step(165): len = 56921.3, overlap = 20.25
PHY-3002 : Step(166): len = 56523.5, overlap = 22.75
PHY-3002 : Step(167): len = 56243.3, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00135797
PHY-3002 : Step(168): len = 56331.3, overlap = 23.25
PHY-3002 : Step(169): len = 56324, overlap = 23
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00271594
PHY-3002 : Step(170): len = 56445.4, overlap = 23
PHY-3002 : Step(171): len = 56469.4, overlap = 23.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003954s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 60688.5, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003880s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 4, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 60818.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6398, tnet num: 1966, tinst num: 816, tnode num: 8639, tedge num: 11203.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 75/1968.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 66992, over cnt = 142(0%), over = 220, worst = 6
PHY-1002 : len = 67752, over cnt = 93(0%), over = 124, worst = 6
PHY-1002 : len = 69136, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 69200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.080232s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (19.5%)

PHY-1001 : Congestion index: top1 = 32.72, top5 = 23.10, top10 = 17.70, top15 = 13.84.
PHY-1001 : End incremental global routing;  0.114862s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (27.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038606s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (80.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.174173s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (35.9%)

OPT-1001 : Current memory(MB): used = 217, reserve = 189, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1745/1968.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 69200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003255s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.72, top5 = 23.10, top10 = 17.70, top15 = 13.84.
OPT-1001 : End congestion update;  0.035534s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (131.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.030806s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 778 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 816 instances, 769 slices, 23 macros(200 instances: 125 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 60763.8, Over = 0
PHY-3001 : End spreading;  0.003130s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 60763.8, Over = 0
PHY-3001 : End incremental legalization;  0.023726s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.9%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.098528s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (63.4%)

OPT-1001 : Current memory(MB): used = 222, reserve = 194, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.029576s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1741/1968.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 69128, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 69152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009182s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.72, top5 = 23.10, top10 = 17.69, top15 = 13.83.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.030131s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.563707s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (38.8%)

RUN-1003 : finish command "place" in  4.298945s wall, 1.468750s user + 0.296875s system = 1.765625s CPU (41.1%)

RUN-1004 : used memory is 188 MB, reserved memory is 159 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |    off     |       off        |        
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 16 thread(s)
RUN-1001 : There are total 818 instances
RUN-1001 : 385 mslices, 384 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1968 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1447 nets have 2 pins
RUN-1001 : 400 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6398, tnet num: 1966, tinst num: 816, tnode num: 8639, tedge num: 11203.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 385 mslices, 384 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 66336, over cnt = 142(0%), over = 218, worst = 6
PHY-1002 : len = 67104, over cnt = 100(0%), over = 133, worst = 6
PHY-1002 : len = 68608, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 68720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089722s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (87.1%)

PHY-1001 : Congestion index: top1 = 33.00, top5 = 23.01, top10 = 17.63, top15 = 13.75.
PHY-1001 : End global routing;  0.124075s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (63.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 209, peak = 252.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 479, peak = 503.
PHY-1001 : End build detailed router design. 2.344598s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (54.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32272, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.792181s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (27.6%)

PHY-1001 : Current memory(MB): used = 535, reserve = 513, peak = 535.
PHY-1001 : End phase 1; 0.795822s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (27.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 178504, over cnt = 31(0%), over = 32, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 514, peak = 536.
PHY-1001 : End initial routed; 0.551955s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (56.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1754(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.217   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.238705s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (39.3%)

PHY-1001 : Current memory(MB): used = 541, reserve = 518, peak = 541.
PHY-1001 : End phase 2; 0.790703s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (51.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178504, over cnt = 31(0%), over = 32, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.009043s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (172.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178328, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.018707s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (83.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178360, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.012153s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1754(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.217   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.254821s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (42.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.121050s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (38.7%)

PHY-1001 : Current memory(MB): used = 556, reserve = 534, peak = 556.
PHY-1001 : End phase 3; 0.509147s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (46.0%)

PHY-1003 : Routed, final wirelength = 178360
PHY-1001 : Current memory(MB): used = 556, reserve = 534, peak = 556.
PHY-1001 : End export database. 0.005573s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  4.579770s wall, 2.156250s user + 0.031250s system = 2.187500s CPU (47.8%)

RUN-1003 : finish command "route" in  4.957772s wall, 2.359375s user + 0.031250s system = 2.390625s CPU (48.2%)

RUN-1004 : used memory is 503 MB, reserved memory is 482 MB, peak memory is 556 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   15
  #output                  18
  #inout                    1

Utilization Statistics
#lut                      790   out of  19600    4.03%
#reg                     1021   out of  19600    5.21%
#le                      1482
  #lut only               461   out of   1482   31.11%
  #reg only               692   out of   1482   46.69%
  #lut&reg                329   out of   1482   22.20%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    16
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         442
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        P74        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1482   |590     |200     |1050    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1072   |281     |107     |866     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |30      |6       |17      |0       |0       |
|    demodu                  |Demodulation                                     |524    |109     |53      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |15      |0       |30      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |30     |9       |0       |30      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |92      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |118    |103     |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |17      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |58     |58      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1411  
    #2          2       262   
    #3          3       124   
    #4          4        14   
    #5        5-10       83   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6398, tnet num: 1966, tinst num: 816, tnode num: 8639, tedge num: 11203.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1966 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 16 threads.
BIT-1002 : Init instances completely, inst num: 816
BIT-1002 : Init pips with 16 threads.
BIT-1002 : Init pips completely, net num: 1968, pip num: 14501
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 16 threads.
BIT-1003 : Generate bitstream completely, there are 1302 valid insts, and 38262 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  1.624655s wall, 11.765625s user + 0.093750s system = 11.859375s CPU (730.0%)

RUN-1004 : used memory is 517 MB, reserved memory is 496 MB, peak memory is 698 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240318_210830.log"
