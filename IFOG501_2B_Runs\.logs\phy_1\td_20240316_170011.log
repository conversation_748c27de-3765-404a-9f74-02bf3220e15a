============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Sat Mar 16 17:00:11 2024

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 16 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1648 instances
RUN-0007 : 395 luts, 985 seqs, 146 mslices, 75 lslices, 32 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2218 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1659 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1646 instances, 395 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7882, tnet num: 2216, tinst num: 1646, tnode num: 11121, tedge num: 13319.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.198511s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (23.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 617742
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1646.
PHY-3001 : End clustering;  0.000008s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 506789, overlap = 20.25
PHY-3002 : Step(2): len = 470571, overlap = 20.25
PHY-3002 : Step(3): len = 424960, overlap = 20.25
PHY-3002 : Step(4): len = 394191, overlap = 15.75
PHY-3002 : Step(5): len = 381301, overlap = 18
PHY-3002 : Step(6): len = 371110, overlap = 18
PHY-3002 : Step(7): len = 358487, overlap = 18
PHY-3002 : Step(8): len = 339398, overlap = 18
PHY-3002 : Step(9): len = 330889, overlap = 18
PHY-3002 : Step(10): len = 320310, overlap = 20.25
PHY-3002 : Step(11): len = 305063, overlap = 20.25
PHY-3002 : Step(12): len = 296253, overlap = 20.25
PHY-3002 : Step(13): len = 290644, overlap = 20.25
PHY-3002 : Step(14): len = 277806, overlap = 20.25
PHY-3002 : Step(15): len = 268556, overlap = 20.25
PHY-3002 : Step(16): len = 263458, overlap = 20.25
PHY-3002 : Step(17): len = 257374, overlap = 20.25
PHY-3002 : Step(18): len = 248251, overlap = 20.25
PHY-3002 : Step(19): len = 243506, overlap = 20.25
PHY-3002 : Step(20): len = 238630, overlap = 20.25
PHY-3002 : Step(21): len = 233395, overlap = 20.25
PHY-3002 : Step(22): len = 227233, overlap = 20.25
PHY-3002 : Step(23): len = 222950, overlap = 20.25
PHY-3002 : Step(24): len = 218361, overlap = 20.25
PHY-3002 : Step(25): len = 213682, overlap = 20.25
PHY-3002 : Step(26): len = 209610, overlap = 20.25
PHY-3002 : Step(27): len = 205590, overlap = 20.25
PHY-3002 : Step(28): len = 200611, overlap = 20.25
PHY-3002 : Step(29): len = 196211, overlap = 20.25
PHY-3002 : Step(30): len = 192050, overlap = 20.25
PHY-3002 : Step(31): len = 188315, overlap = 20.25
PHY-3002 : Step(32): len = 180282, overlap = 20.25
PHY-3002 : Step(33): len = 176579, overlap = 20.25
PHY-3002 : Step(34): len = 174076, overlap = 20.25
PHY-3002 : Step(35): len = 164808, overlap = 20.25
PHY-3002 : Step(36): len = 154899, overlap = 20.25
PHY-3002 : Step(37): len = 152782, overlap = 20.25
PHY-3002 : Step(38): len = 149330, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000135815
PHY-3002 : Step(39): len = 151522, overlap = 13.5
PHY-3002 : Step(40): len = 150689, overlap = 11.25
PHY-3002 : Step(41): len = 148176, overlap = 11.25
PHY-3002 : Step(42): len = 145337, overlap = 15.75
PHY-3002 : Step(43): len = 143187, overlap = 6.75
PHY-3002 : Step(44): len = 138586, overlap = 6.75
PHY-3002 : Step(45): len = 135640, overlap = 13.5
PHY-3002 : Step(46): len = 133126, overlap = 13.5
PHY-3002 : Step(47): len = 128412, overlap = 11.25
PHY-3002 : Step(48): len = 126721, overlap = 13.5
PHY-3002 : Step(49): len = 122909, overlap = 15.75
PHY-3002 : Step(50): len = 121332, overlap = 6.75
PHY-3002 : Step(51): len = 114260, overlap = 11.25
PHY-3002 : Step(52): len = 112640, overlap = 11.25
PHY-3002 : Step(53): len = 111181, overlap = 9
PHY-3002 : Step(54): len = 108099, overlap = 11.25
PHY-3002 : Step(55): len = 100518, overlap = 15.75
PHY-3002 : Step(56): len = 99449.5, overlap = 6.75
PHY-3002 : Step(57): len = 97848.1, overlap = 9
PHY-3002 : Step(58): len = 96466.7, overlap = 9
PHY-3002 : Step(59): len = 94995.3, overlap = 6.75
PHY-3002 : Step(60): len = 94423.2, overlap = 9
PHY-3002 : Step(61): len = 92384.6, overlap = 9
PHY-3002 : Step(62): len = 90718.7, overlap = 9.8125
PHY-3002 : Step(63): len = 89295, overlap = 14.375
PHY-3002 : Step(64): len = 88808.6, overlap = 14.375
PHY-3002 : Step(65): len = 87016.1, overlap = 7.5625
PHY-3002 : Step(66): len = 85445.5, overlap = 7.5625
PHY-3002 : Step(67): len = 82942, overlap = 9.75
PHY-3002 : Step(68): len = 82429.5, overlap = 9.75
PHY-3002 : Step(69): len = 80162.4, overlap = 7.75
PHY-3002 : Step(70): len = 78064.6, overlap = 10.0625
PHY-3002 : Step(71): len = 76859.1, overlap = 10
PHY-3002 : Step(72): len = 76279, overlap = 12.3125
PHY-3002 : Step(73): len = 74800.5, overlap = 12.625
PHY-3002 : Step(74): len = 73899.4, overlap = 10.25
PHY-3002 : Step(75): len = 73607.3, overlap = 10.4375
PHY-3002 : Step(76): len = 72142.8, overlap = 10.375
PHY-3002 : Step(77): len = 71008.2, overlap = 12.4375
PHY-3002 : Step(78): len = 69434.6, overlap = 12.6875
PHY-3002 : Step(79): len = 68524.1, overlap = 10.625
PHY-3002 : Step(80): len = 67624.7, overlap = 10.5625
PHY-3002 : Step(81): len = 66741.3, overlap = 12.9375
PHY-3002 : Step(82): len = 64966, overlap = 10.875
PHY-3002 : Step(83): len = 63593, overlap = 11.5
PHY-3002 : Step(84): len = 62423.3, overlap = 11.7188
PHY-3002 : Step(85): len = 61724.3, overlap = 14.4062
PHY-3002 : Step(86): len = 60918.4, overlap = 14.5312
PHY-3002 : Step(87): len = 60632.1, overlap = 14.5312
PHY-3002 : Step(88): len = 60614.4, overlap = 14.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00027163
PHY-3002 : Step(89): len = 60696, overlap = 12.2812
PHY-3002 : Step(90): len = 60780, overlap = 12.3438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00054326
PHY-3002 : Step(91): len = 60697.6, overlap = 12.4062
PHY-3002 : Step(92): len = 60700.1, overlap = 12.4688
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004754s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.041706s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 64062.9, overlap = 17.2188
PHY-3002 : Step(94): len = 62755.7, overlap = 16.9375
PHY-3002 : Step(95): len = 61533.4, overlap = 17.4062
PHY-3002 : Step(96): len = 60407.8, overlap = 17.375
PHY-3002 : Step(97): len = 59179.2, overlap = 17.375
PHY-3002 : Step(98): len = 57547.6, overlap = 17.7188
PHY-3002 : Step(99): len = 56354.3, overlap = 18.5938
PHY-3002 : Step(100): len = 54722.2, overlap = 19.6562
PHY-3002 : Step(101): len = 54059.7, overlap = 19.0938
PHY-3002 : Step(102): len = 53240.3, overlap = 18.9688
PHY-3002 : Step(103): len = 52841.4, overlap = 19.7812
PHY-3002 : Step(104): len = 51987.9, overlap = 20.5625
PHY-3002 : Step(105): len = 51698.9, overlap = 20.6875
PHY-3002 : Step(106): len = 51344.9, overlap = 20.9062
PHY-3002 : Step(107): len = 50923.1, overlap = 21.0312
PHY-3002 : Step(108): len = 50139.9, overlap = 21.8438
PHY-3002 : Step(109): len = 49488.3, overlap = 22.3438
PHY-3002 : Step(110): len = 48853.6, overlap = 23.3438
PHY-3002 : Step(111): len = 48375.9, overlap = 22.5938
PHY-3002 : Step(112): len = 48097, overlap = 22.0312
PHY-3002 : Step(113): len = 47758, overlap = 21.25
PHY-3002 : Step(114): len = 47615.5, overlap = 21.3438
PHY-3002 : Step(115): len = 47370.4, overlap = 20.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00112216
PHY-3002 : Step(116): len = 47271.5, overlap = 20.875
PHY-3002 : Step(117): len = 47227.5, overlap = 20.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00224433
PHY-3002 : Step(118): len = 47188.9, overlap = 20.4375
PHY-3002 : Step(119): len = 47178.8, overlap = 19.9062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050720s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (30.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.84311e-05
PHY-3002 : Step(120): len = 47587, overlap = 64.25
PHY-3002 : Step(121): len = 48309.7, overlap = 65.6562
PHY-3002 : Step(122): len = 49032.4, overlap = 64.25
PHY-3002 : Step(123): len = 48866.2, overlap = 59.9375
PHY-3002 : Step(124): len = 48978.7, overlap = 59.8438
PHY-3002 : Step(125): len = 49076.3, overlap = 59.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000136862
PHY-3002 : Step(126): len = 49278.6, overlap = 58.5938
PHY-3002 : Step(127): len = 49412.7, overlap = 57.75
PHY-3002 : Step(128): len = 50565.7, overlap = 48.875
PHY-3002 : Step(129): len = 52185.7, overlap = 44.6562
PHY-3002 : Step(130): len = 53063.4, overlap = 37.4062
PHY-3002 : Step(131): len = 52513.5, overlap = 40.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000273724
PHY-3002 : Step(132): len = 52559.3, overlap = 40.9688
PHY-3002 : Step(133): len = 52659.8, overlap = 36.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000547449
PHY-3002 : Step(134): len = 52677.5, overlap = 35.25
PHY-3002 : Step(135): len = 54038.9, overlap = 35.9375
PHY-3002 : Step(136): len = 54552.2, overlap = 35.5938
PHY-3002 : Step(137): len = 54789.2, overlap = 35.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7882, tnet num: 2216, tinst num: 1646, tnode num: 11121, tedge num: 13319.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.50 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2218.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 58784, over cnt = 262(0%), over = 1036, worst = 18
PHY-1001 : End global iterations;  0.063489s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 43.58, top5 = 25.89, top10 = 16.89, top15 = 12.12.
PHY-1001 : End incremental global routing;  0.103665s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (15.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050488s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.178333s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (8.8%)

OPT-1001 : Current memory(MB): used = 208, reserve = 180, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1722/2218.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 58784, over cnt = 262(0%), over = 1036, worst = 18
PHY-1002 : len = 66080, over cnt = 176(0%), over = 380, worst = 15
PHY-1002 : len = 69632, over cnt = 21(0%), over = 33, worst = 5
PHY-1002 : len = 70064, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 70192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.069458s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 38.71, top5 = 25.93, top10 = 18.46, top15 = 13.71.
OPT-1001 : End congestion update;  0.100488s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.040924s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Start: WNS 873 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 873 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.144210s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Current memory(MB): used = 211, reserve = 184, peak = 211.
OPT-1001 : End physical optimization;  0.528722s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (8.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 395 LUT to BLE ...
SYN-4008 : Packed 395 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 124 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 679 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1074/1400 primitive instances ...
PHY-3001 : End packing;  0.039403s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 395 mslices, 395 lslices, 32 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2050 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1499 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 790 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55049.8, Over = 64.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6661, tnet num: 2048, tinst num: 835, tnode num: 9013, tedge num: 11687.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.224504s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (7.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.46877e-05
PHY-3002 : Step(138): len = 54454.5, overlap = 64.25
PHY-3002 : Step(139): len = 53933.8, overlap = 64.5
PHY-3002 : Step(140): len = 53438.8, overlap = 62.25
PHY-3002 : Step(141): len = 53081.7, overlap = 62
PHY-3002 : Step(142): len = 52970.8, overlap = 62.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.93754e-05
PHY-3002 : Step(143): len = 53254.4, overlap = 59.75
PHY-3002 : Step(144): len = 53621.4, overlap = 58.75
PHY-3002 : Step(145): len = 54429.6, overlap = 53.25
PHY-3002 : Step(146): len = 55036.1, overlap = 50.5
PHY-3002 : Step(147): len = 55089.7, overlap = 51.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000138751
PHY-3002 : Step(148): len = 55509.6, overlap = 50.25
PHY-3002 : Step(149): len = 55775.7, overlap = 49.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.080163s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Trial Legalized: Len = 69703.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.035976s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (43.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000659405
PHY-3002 : Step(150): len = 67722, overlap = 5.25
PHY-3002 : Step(151): len = 66071.7, overlap = 10.75
PHY-3002 : Step(152): len = 64034.7, overlap = 14.5
PHY-3002 : Step(153): len = 63165.8, overlap = 14.25
PHY-3002 : Step(154): len = 62791.7, overlap = 15.5
PHY-3002 : Step(155): len = 62256.9, overlap = 16.75
PHY-3002 : Step(156): len = 61806.1, overlap = 18.25
PHY-3002 : Step(157): len = 61397.5, overlap = 18.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00129408
PHY-3002 : Step(158): len = 61887.9, overlap = 19
PHY-3002 : Step(159): len = 61909.8, overlap = 19.25
PHY-3002 : Step(160): len = 61909.8, overlap = 19.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00258817
PHY-3002 : Step(161): len = 62070.9, overlap = 19.25
PHY-3002 : Step(162): len = 62221.4, overlap = 18.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007729s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 66500.1, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004177s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 12 instances has been re-located, deltaX = 3, deltaY = 8, maxDist = 2.
PHY-3001 : Final: Len = 66840.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6661, tnet num: 2048, tinst num: 835, tnode num: 9013, tedge num: 11687.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 54/2050.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 73800, over cnt = 141(0%), over = 203, worst = 5
PHY-1002 : len = 74464, over cnt = 51(0%), over = 59, worst = 3
PHY-1002 : len = 74984, over cnt = 12(0%), over = 15, worst = 2
PHY-1002 : len = 75224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.086632s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.66, top5 = 23.50, top10 = 18.39, top15 = 14.47.
PHY-1001 : End incremental global routing;  0.123914s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043721s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.189929s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Current memory(MB): used = 216, reserve = 188, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1810/2050.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 75224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003691s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.66, top5 = 23.50, top10 = 18.39, top15 = 14.47.
OPT-1001 : End congestion update;  0.036492s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.035307s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Start: WNS 873 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 32 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 799 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 790 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 66823.8, Over = 0
PHY-3001 : End spreading;  0.003676s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 66823.8, Over = 0
PHY-3001 : End incremental legalization;  0.025499s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Iter 1: improved WNS 873 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 873 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.108010s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Current memory(MB): used = 220, reserve = 193, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.037213s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (42.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1798/2050.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 75192, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 75192, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 75208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.014218s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 23.47, top10 = 18.37, top15 = 14.46.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034260s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 873 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.344828
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 873ps with logic level 1 
RUN-1001 :       #2 path slack 873ps with logic level 1 
RUN-1001 :       #3 path slack 873ps with logic level 1 
RUN-1001 :       #4 path slack 873ps with logic level 1 
RUN-1001 :       #5 path slack 873ps with logic level 1 
RUN-1001 :       #6 path slack 873ps with logic level 1 
RUN-1001 :       #7 path slack 873ps with logic level 1 
RUN-1001 :       #8 path slack 873ps with logic level 1 
RUN-1001 :       #9 path slack 873ps with logic level 1 
RUN-1001 :       #10 path slack 873ps with logic level 1 
OPT-1001 : End physical optimization;  0.624844s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (10.0%)

RUN-1003 : finish command "place" in  4.170969s wall, 0.515625s user + 0.187500s system = 0.703125s CPU (16.9%)

RUN-1004 : used memory is 194 MB, reserved memory is 166 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |    off     |       off        |        
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 16 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 395 mslices, 395 lslices, 32 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2050 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1499 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6661, tnet num: 2048, tinst num: 835, tnode num: 9013, tedge num: 11687.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 395 mslices, 395 lslices, 32 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 73160, over cnt = 140(0%), over = 205, worst = 4
PHY-1002 : len = 73864, over cnt = 56(0%), over = 68, worst = 3
PHY-1002 : len = 74704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087483s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (17.9%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 23.31, top10 = 18.26, top15 = 14.37.
PHY-1001 : End global routing;  0.124728s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (25.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 206, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 481, peak = 503.
PHY-1001 : End build detailed router design. 2.470605s wall, 0.828125s user + 0.031250s system = 0.859375s CPU (34.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 37568, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.784907s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (39.8%)

PHY-1001 : Current memory(MB): used = 534, reserve = 514, peak = 534.
PHY-1001 : End phase 1; 0.788428s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (39.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 192296, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 515, peak = 536.
PHY-1001 : End initial routed; 0.588730s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (39.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1819(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.371   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.254372s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (61.4%)

PHY-1001 : Current memory(MB): used = 537, reserve = 516, peak = 537.
PHY-1001 : End phase 2; 0.843145s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (46.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 192296, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.010487s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 192296, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.015852s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 192392, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.013604s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1819(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.371   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.251141s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (18.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.130378s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (47.9%)

PHY-1001 : Current memory(MB): used = 549, reserve = 528, peak = 549.
PHY-1001 : End phase 3; 0.515013s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (27.3%)

PHY-1003 : Routed, final wirelength = 192392
PHY-1001 : Current memory(MB): used = 550, reserve = 528, peak = 550.
PHY-1001 : End export database. 0.009244s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  4.762298s wall, 1.718750s user + 0.031250s system = 1.750000s CPU (36.7%)

RUN-1003 : finish command "route" in  5.143396s wall, 1.812500s user + 0.046875s system = 1.859375s CPU (36.2%)

RUN-1004 : used memory is 522 MB, reserved memory is 502 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   15
  #output                  18
  #inout                    1

Utilization Statistics
#lut                      841   out of  19600    4.29%
#reg                     1074   out of  19600    5.48%
#le                      1520
  #lut only               446   out of   1520   29.34%
  #reg only               679   out of   1520   44.67%
  #lut&reg                395   out of   1520   25.99%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       32   out of     66   48.48%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         113
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT        P23        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[10]      INPUT        P37        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[9]       INPUT         P8        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[8]       INPUT        P84        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[7]       INPUT        P68        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[6]       INPUT        P27        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[5]       INPUT        P41        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[4]       INPUT         P4        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[3]       INPUT        P72        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[2]       INPUT        P57        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[1]       INPUT        P38        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
    clk_in         INPUT        P34        LVCMOS25          N/A           N/A        NONE    
  DA_DATA[13]     OUTPUT         P5        LVCMOS25           8            N/A        OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS25           8            N/A        OREG    
  DA_DATA[11]     OUTPUT        P18        LVCMOS33           8            N/A        OREG    
  DA_DATA[10]     OUTPUT         P3        LVCMOS25           8            N/A        OREG    
  DA_DATA[9]      OUTPUT        P76        LVCMOS25           8            N/A        OREG    
  DA_DATA[8]      OUTPUT        P77        LVCMOS25           8            N/A        OREG    
  DA_DATA[7]      OUTPUT        P49        LVCMOS25           8            N/A        OREG    
  DA_DATA[6]      OUTPUT        P74        LVCMOS25           8            N/A        OREG    
  DA_DATA[5]      OUTPUT        P75        LVCMOS25           8            N/A        OREG    
  DA_DATA[4]      OUTPUT        P52        LVCMOS25           8            N/A        OREG    
  DA_DATA[3]      OUTPUT        P59        LVCMOS25           8            N/A        OREG    
  DA_DATA[2]      OUTPUT        P31        LVCMOS25           8            N/A        OREG    
  DA_DATA[1]      OUTPUT         P2        LVCMOS25           8            N/A        OREG    
  DA_DATA[0]      OUTPUT        P69        LVCMOS25           8            N/A        OREG    
      TXD         OUTPUT        P39        LVCMOS25           8            N/A        OREG    
  TxTransmit      OUTPUT        P81        LVCMOS25           8            N/A        OREG    
    clk_ADo       OUTPUT        P29        LVCMOS25           8            N/A       ODDRX1   
    clk_DA        OUTPUT        P86        LVCMOS25           8            N/A        OREG    
      dq           INOUT        P13        LVCMOS33           8            N/A        TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1520   |620     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1121   |320     |128     |919     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |33      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |521    |110     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |10      |0       |25      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |32     |16      |0       |32      |0       |0       |
|    integ                   |Integration                                      |137    |17      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |88     |30      |21      |83      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |111     |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |102    |89      |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |25     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |42     |42      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1465  
    #2          2       295   
    #3          3       121   
    #4          4        13   
    #5        5-10       84   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6661, tnet num: 2048, tinst num: 835, tnode num: 9013, tedge num: 11687.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 16 threads.
BIT-1002 : Init instances completely, inst num: 835
BIT-1002 : Init pips with 16 threads.
BIT-1002 : Init pips completely, net num: 2050, pip num: 15250
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 16 threads.
BIT-1003 : Generate bitstream completely, there are 1367 valid insts, and 40025 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  1.621813s wall, 8.812500s user + 0.046875s system = 8.859375s CPU (546.3%)

RUN-1004 : used memory is 534 MB, reserved memory is 511 MB, peak memory is 706 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240316_170011.log"
