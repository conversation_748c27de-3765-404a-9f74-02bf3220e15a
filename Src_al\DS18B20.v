`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	DS18B20
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	Serial port transmission-- protocol
// Revision 1.01 - File Created
// Additional Comments: 
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module DS18B20(
	input 			clk,	//System clock，100M
	input			rst_n,   //Low-level effective reset signal	
	output	reg		rd_done,	//Finish reading the temperature data
	output	reg		rd_flag,	//Finish reading the temperature data
	inout			dq,	//1-wire bus (bidirectional signal)
	output 	[15:0]  temp_data	//dataout
);
 
//------------<Parameter definition>----------------------------------------------
//State machine state definition
localparam	INIT1		= 6'b000001,
			WR_CMD      = 6'b000010,
			WAIT  		= 6'b000100,
			INIT2  		= 6'b001000,
			RD_CMD  	= 6'b010000,
			RD_DATA  	= 6'b100000;
		
//Time parameter definition
localparam	T_INIT = 1000,			//Maximum initialization time, unitus
			T_WAIT = 780_000;			//Conversion wait delay, unitus
	
//Command definition	
localparam 	WR_CMD_DATA = 16'h44CC, 		//Skip ROM and temperature conversion commands, low first
			RD_CMD_DATA = 16'hBECC; 		//Skip ROM and read temperature commands, low first
					
//------------<reg Define>----------------------------------------------		
reg	[5:0]	cur_state;					//Present state
reg	[5:0]	next_state;					//Secondary state
reg	[7:0]	cnt;					//100 frequency division counter 1Mhz(1us)				
reg			dq_out;					//Bidirectional bus output
reg			dq_en;					//Bi-directional bus output enable, 1 output, 0 high resistance state
reg			flag_ack;					//Slave response sign signal
reg			clk_us;					//Us clock
reg [19:0]	cnt_us;					//us Counter, which can represent a maximum of 1048ms
reg [3:0]	bit_cnt;					//Receive data counter
reg [15:0]	data_temp;					//Storage of read temperature data
reg [15:0]	data;					//Unprocessed raw temperature data
				
//------------<wire Define>----------------------------------------------				
wire		dq_in;					//Bidirectional bus input
 
//-----------------------------------------------------------------------
//--Two-way port usage
//-----------------------------------------------------------------------
assign	dq_in = dq;//dq;				//In the case of high resistance, the data on the bus is assigned to dq_in
assign	dq =  dq_en? dq_out : 1'bz;			//Enable 1 for output, 0 for high resistance state
 
//-----------------------------------------------------------------------
//--us Clock generation, because the timing is in us units, so it is more convenient to generate a 1us clock
//-----------------------------------------------------------------------
//88 Frequency division counting
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		cnt <= 8'd0;
	else if(cnt == 8'd59)					//500ns zeroing every 30 clocks
		cnt <= 8'd0;
	else
		cnt <= cnt + 1'd1;
end

//Generate 1us clock
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		clk_us <= 1'b0;
	else  if(cnt == 8'd59)					//Per 500ns
		clk_us <= ~clk_us;                  //Clock reversal
	else
		clk_us <= clk_us;
end
 
//-----------------------------------------------------------------------
//--Three-stage state machine
//-----------------------------------------------------------------------
 
//The first paragraph of the state machine: synchronous timing describes the state transition
always @(posedge clk_us or negedge rst_n)begin
	if(!rst_n)begin		
		cur_state <= INIT1;	
	end
	else begin
		cur_state <= next_state;
	end
end
 
//The second part of the state machine: combinational logic judges the condition of state transition,
//describes the law of state transition and output
always @(*)begin
	if(~rst_n)
		next_state = INIT1;	
	else begin
	case(cur_state)
		INIT1:
			begin
				if(cnt_us == T_INIT && flag_ack)				
					next_state = WR_CMD;						
				else	
					next_state = INIT1;							
			end	
		WR_CMD:
			begin	
				if(bit_cnt == 4'd15 && cnt_us == 20'd62)		
					next_state = WAIT;							
				else	
					next_state = WR_CMD;						
			end	
		WAIT:
			begin	
				if(cnt_us == T_WAIT)							
					next_state = INIT2;	
				else	
					next_state = WAIT;	
			end	
		INIT2:
			begin	
				if(cnt_us == T_INIT && flag_ack)				
					next_state = RD_CMD;
				else
					next_state = INIT2;
			end
		RD_CMD:
			begin
				if(bit_cnt == 4'd15 && cnt_us == 20'd62)		
					next_state = RD_DATA;						
				else	
					next_state = RD_CMD;	
			end	
		RD_DATA:
			begin	
				if(bit_cnt == 4'd15 && cnt_us == 20'd62)		
					next_state = INIT1;							
				else	
					next_state = RD_DATA;	
			end			
		default:next_state = INIT1;							
	endcase
	end
end	
 
//the third segment of the state machine: temporal logic description output
always @(posedge clk_us or negedge rst_n)begin
	if(!rst_n)begin											
		dq_en 		<= 1'b0;
		dq_out 		<= 1'b0;
		rd_done		<= 1'b0;
		flag_ack 	<= 1'b0;
		cnt_us 		<= 20'd0;
		bit_cnt 		<= 4'd0;
		data_temp	<= 16'h0000;
		data			<= 16'h0000;
	end
	else begin 	
		case(cur_state)
			INIT1:
				begin
					if(cnt_us == T_INIT)begin					
						cnt_us <= 20'd0;						
						flag_ack <= 1'b0;	
					end
					else begin					
						cnt_us <= cnt_us + 1'd1;				
						if(cnt_us <= 20'd499)begin				
							dq_en <= 1'b1;							
							dq_out <= 1'b0;
						end
						else begin								
							rd_flag	<= 1'b1;	
							dq_en <= 1'b0;						
							if (cnt_us == 20'd570 && !dq_in)	begin
								flag_ack <= 1'b1;	
							end
							else begin
								flag_ack <= flag_ack;	
							end
						end	
					end
				end
			WR_CMD:
				begin
					if(cnt_us == 20'd62)begin						
						cnt_us <= 20'd0;							
						dq_en <= 1'b0;								
						if(bit_cnt == 4'd15)						
							bit_cnt <= 4'd0;						
						else										
							bit_cnt <= bit_cnt + 1'd1;				
					end	
					else begin										
						cnt_us <= cnt_us + 1'd1;					
						if(cnt_us <= 20'd10)begin					
							dq_en <= 1'b1;						
							dq_out <= 1'b0;	
						end
						else begin										
							if (WR_CMD_DATA[bit_cnt] == 1'b0)begin	
								dq_en <= 1'b1;						
								dq_out <= 1'b0;									
							end
							else if(WR_CMD_DATA[bit_cnt] == 1'b1)begin
								dq_en <= 1'b0;						
								dq_out <= 1'b0;								
							end	
						end	
					end		
				end		
			WAIT:
				begin										
					dq_en <= 1'b1;							
					dq_out <= 1'b0;							
					if(cnt_us == T_WAIT)					
						cnt_us <= 20'd0;
					else
						cnt_us <= cnt_us + 1'd1;
				end	
			INIT2:
				begin										
					if(cnt_us == T_INIT)begin				
						cnt_us <= 20'd0;
						flag_ack <= 1'b0;
					end
					else begin
						cnt_us <= cnt_us + 1'd1;
						if(cnt_us <= 20'd499)begin
							dq_en <= 1'b1;						
							dq_out <= 1'b0;
						end
						else begin
							dq_en <= 1'b0;												
							if (cnt_us == 20'd570 && !dq_in)
								flag_ack <= 1'b1;
							else 
								flag_ack <= flag_ack;
						end	
					end
				end	
			RD_CMD:
				begin										
					if(cnt_us == 20'd62)begin
						cnt_us <= 20'd0;
						dq_en <= 1'b0;
						if(bit_cnt == 4'd15)
							bit_cnt <= 4'd0;
						else
							bit_cnt <= bit_cnt + 1'd1;
					end
					else begin
						cnt_us <= cnt_us + 1'd1;
						if(cnt_us <= 20'd10)begin
							dq_en <= 1'b1;							
							dq_out <= 1'b0;
						end
						else begin					
							if (RD_CMD_DATA[bit_cnt] == 1'b0)begin
								dq_en <= 1'b1;						
								dq_out <= 1'b0;														
							end
							else if(RD_CMD_DATA[bit_cnt] == 1'b1)begin
								dq_en <= 1'b0;						
								dq_out <= 1'b0;												
							end	
						end	
					end
				end	
			RD_DATA:
				begin									
					if(cnt_us == 20'd62)begin			
						cnt_us <= 20'd0;				
						dq_en <= 1'b0;					
						if(bit_cnt == 4'd15)begin		
							bit_cnt <= 4'd0;			
							data <= data_temp;			
							rd_done <= 1'b1;
						end
						else begin					
							data <= data;				
							rd_done <= 1'b0;						
							bit_cnt <= bit_cnt + 1'd1;	
						end
					end
					else begin										
						cnt_us <= cnt_us + 1'd1;					
						if(cnt_us <= 20'd1)begin					
							dq_en <= 1'b1;							
							dq_out <= 1'b0;
						end
						else begin									
							dq_en <= 1'b0;							
							if(cnt_us == 20'd10)					
								data_temp <= {dq,data_temp[15:1]};	
							else 
								data_temp <= data_temp;
						end	
					end
				end
			default:;		
		endcase
	end
end

assign temp_data = data[15:0];
  
endmodule