=========================================================================================================
Auto created by Tang Dynasty v5.6.71036
   Copyright (c) 2012-2023 Anlogic Inc.
Thu Jul 31 20:14:05 2025
=========================================================================================================


Top Model:                IFOG501_2B                                                      
Device:                   eagle_s20                                                       
Timing Constraint File:   ../../Constraints/IFOG_11FB.sdc                                 
STA Level:                Detail                                                          
Speed Grade:              NA                                                              

=========================================================================================================
Timing constraint:        clock: clk_in                                                   
Clock = clk_in, period 50ns, rising at 0ns, falling at 25ns

0 endpoints analyzed totally, and 0 paths analyzed
0 errors detected : 0 setup errors (TNS = 0), 0 hold errors (TNS = 0)
Minimum period is 0ns
---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        clock: CLK120/pll_inst.clkc[0]                                  
Clock = CLK120/pll_inst.clkc[0], period 8.333ns, rising at 0ns, falling at 4.166ns

2206 endpoints analyzed totally, and 35862 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Minimum period is 8.175ns
---------------------------------------------------------------------------------------------------------

Paths for end point u_uart/U2/tx_data_dy_b[3]_syn_42 (7 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     0.158 ns                                                        
 Start Point:             wendu/reg4_syn_121.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/tx_data_dy_b[3]_syn_42.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         3.378ns  (logic 1.557ns, net 1.821ns, 46% logic)                
 Logic Levels:            3 ( LUT5=2 LUT4=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_121.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_121.q[1]                                     clk2q                   0.146 r     3.592
 u_uart/U2/tx_data_dy_b[3]_syn_49.c[1] (wendu/data[3])       net  (fanout = 3)       0.602 r     4.194      ../../Src_al/DS18B20.v(54)
 u_uart/U2/tx_data_dy_b[3]_syn_49.f[1]                       cell (LUT4)             0.251 r     4.445
 u_uart/U2/tx_data_dy_b[3]_syn_46.a[1] (u_uart/U2/tx_data_dy_b[3]_syn_14) net  (fanout = 2)       0.594 r     5.039                    
 u_uart/U2/tx_data_dy_b[3]_syn_46.fx[0]                      cell (LUT5)             0.618 r     5.657
 u_uart/U2/tx_data_dy_b[3]_syn_42.a[0] (u_uart/U2/tx_data_dy_b[3]_syn_20) net  (fanout = 1)       0.625 r     6.282      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/tx_data_dy_b[3]_syn_42                            path2reg0 (LUT5)        0.542       6.824
 Arrival time                                                                        6.824                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/tx_data_dy_b[3]_syn_42.clk (signal_process/clk)   net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.158ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.158 ns                                                        
 Start Point:             wendu/reg4_syn_121.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/tx_data_dy_b[3]_syn_42.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         3.378ns  (logic 1.557ns, net 1.821ns, 46% logic)                
 Logic Levels:            3 ( LUT5=2 LUT4=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_121.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_121.q[1]                                     clk2q                   0.146 r     3.592
 u_uart/U2/tx_data_dy_b[3]_syn_49.c[1] (wendu/data[3])       net  (fanout = 3)       0.602 r     4.194      ../../Src_al/DS18B20.v(54)
 u_uart/U2/tx_data_dy_b[3]_syn_49.f[1]                       cell (LUT4)             0.251 r     4.445
 u_uart/U2/tx_data_dy_b[3]_syn_46.a[0] (u_uart/U2/tx_data_dy_b[3]_syn_14) net  (fanout = 2)       0.594 r     5.039                    
 u_uart/U2/tx_data_dy_b[3]_syn_46.fx[0]                      cell (LUT5)             0.618 r     5.657
 u_uart/U2/tx_data_dy_b[3]_syn_42.a[0] (u_uart/U2/tx_data_dy_b[3]_syn_20) net  (fanout = 1)       0.625 r     6.282      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/tx_data_dy_b[3]_syn_42                            path2reg0 (LUT5)        0.542       6.824
 Arrival time                                                                        6.824                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/tx_data_dy_b[3]_syn_42.clk (signal_process/clk)   net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.158ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.314 ns                                                        
 Start Point:             wendu/reg4_syn_112.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/tx_data_dy_b[3]_syn_42.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         3.222ns  (logic 1.511ns, net 1.711ns, 46% logic)                
 Logic Levels:            3 ( LUT5=2 LUT4=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_112.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_112.q[0]                                     clk2q                   0.146 r     3.592
 u_uart/U2/tx_data_dy_b[3]_syn_49.d[1] (wendu/data[11])      net  (fanout = 3)       0.492 r     4.084      ../../Src_al/DS18B20.v(54)
 u_uart/U2/tx_data_dy_b[3]_syn_49.f[1]                       cell (LUT4)             0.205 r     4.289
 u_uart/U2/tx_data_dy_b[3]_syn_46.a[1] (u_uart/U2/tx_data_dy_b[3]_syn_14) net  (fanout = 2)       0.594 r     4.883                    
 u_uart/U2/tx_data_dy_b[3]_syn_46.fx[0]                      cell (LUT5)             0.618 r     5.501
 u_uart/U2/tx_data_dy_b[3]_syn_42.a[0] (u_uart/U2/tx_data_dy_b[3]_syn_20) net  (fanout = 1)       0.625 r     6.126      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/tx_data_dy_b[3]_syn_42                            path2reg0 (LUT5)        0.542       6.668
 Arrival time                                                                        6.668                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/tx_data_dy_b[3]_syn_42.clk (signal_process/clk)   net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.314ns          

---------------------------------------------------------------------------------------------------------

Paths for end point u_uart/U2/reg0_syn_51 (4 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     0.203 ns                                                        
 Start Point:             wendu/reg4_syn_103.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/reg0_syn_51.b[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         3.333ns  (logic 1.381ns, net 1.952ns, 41% logic)                
 Logic Levels:            3 ( LUT5=2 LUT4=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_103.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_103.q[0]                                     clk2q                   0.146 r     3.592
 signal_process/rs422/reg5_syn_239.d[0] (wendu/data[14])     net  (fanout = 3)       0.620 r     4.212      ../../Src_al/DS18B20.v(54)
 signal_process/rs422/reg5_syn_239.f[0]                      cell (LUT4)             0.262 r     4.474
 u_uart/U2/reg1_syn_59.a[0] (u_uart/U2/tx_data_dy_b[6]_syn_7) net  (fanout = 1)       0.594 r     5.068                    
 u_uart/U2/reg1_syn_59.f[0]                                  cell (LUT5)             0.424 r     5.492
 u_uart/U2/reg0_syn_51.b[1] (u_uart/U2/tx_data_dy_b[6]_syn_13) net  (fanout = 1)       0.738 r     6.230      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/reg0_syn_51                                       path2reg1 (LUT5)        0.549       6.779
 Arrival time                                                                        6.779                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/reg0_syn_51.clk (signal_process/clk)              net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.203ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.245 ns                                                        
 Start Point:             wendu/reg4_syn_103.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/reg0_syn_51.b[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         3.291ns  (logic 1.467ns, net 1.824ns, 44% logic)                
 Logic Levels:            3 ( LUT5=2 LUT4=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_103.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_103.q[1]                                     clk2q                   0.146 r     3.592
 signal_process/rs422/reg5_syn_239.c[0] (wendu/data[6])      net  (fanout = 3)       0.492 r     4.084      ../../Src_al/DS18B20.v(54)
 signal_process/rs422/reg5_syn_239.f[0]                      cell (LUT4)             0.348 r     4.432
 u_uart/U2/reg1_syn_59.a[0] (u_uart/U2/tx_data_dy_b[6]_syn_7) net  (fanout = 1)       0.594 r     5.026                    
 u_uart/U2/reg1_syn_59.f[0]                                  cell (LUT5)             0.424 r     5.450
 u_uart/U2/reg0_syn_51.b[1] (u_uart/U2/tx_data_dy_b[6]_syn_13) net  (fanout = 1)       0.738 r     6.188      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/reg0_syn_51                                       path2reg1 (LUT5)        0.549       6.737
 Arrival time                                                                        6.737                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/reg0_syn_51.clk (signal_process/clk)              net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.245ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.328 ns                                                        
 Start Point:             wendu/reg4_syn_103.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/reg0_syn_51.b[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         3.208ns  (logic 1.388ns, net 1.820ns, 43% logic)                
 Logic Levels:            3 ( LUT5=3 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_103.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_103.q[1]                                     clk2q                   0.146 r     3.592
 u_uart/U2/reg1_syn_59.d[1] (wendu/data[6])                  net  (fanout = 3)       0.344 r     3.936      ../../Src_al/DS18B20.v(54)
 u_uart/U2/reg1_syn_59.f[1]                                  cell (LUT5)             0.262 r     4.198
 u_uart/U2/reg1_syn_59.b[0] (u_uart/U2/tx_data_dy_b[6]_syn_11) net  (fanout = 1)       0.738 r     4.936                    
 u_uart/U2/reg1_syn_59.f[0]                                  cell (LUT5)             0.431 r     5.367
 u_uart/U2/reg0_syn_51.b[1] (u_uart/U2/tx_data_dy_b[6]_syn_13) net  (fanout = 1)       0.738 r     6.105      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/reg0_syn_51                                       path2reg1 (LUT5)        0.549       6.654
 Arrival time                                                                        6.654                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/reg0_syn_51.clk (signal_process/clk)              net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.328ns          

---------------------------------------------------------------------------------------------------------

Paths for end point u_uart/U2/tx_data_dy_b[4]_syn_42 (4 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     0.493 ns                                                        
 Start Point:             wendu/reg4_syn_121.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/tx_data_dy_b[4]_syn_42.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         3.043ns  (logic 1.381ns, net 1.662ns, 45% logic)                
 Logic Levels:            3 ( LUT5=2 LUT4=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_121.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_121.q[0]                                     clk2q                   0.146 r     3.592
 u_uart/U2/tx_data_dy_b[4]_syn_48.d[0] (wendu/data[4])       net  (fanout = 3)       0.612 r     4.204      ../../Src_al/DS18B20.v(54)
 u_uart/U2/tx_data_dy_b[4]_syn_48.f[0]                       cell (LUT4)             0.262 r     4.466
 u_uart/U2/tx_data_dy_b[2]_syn_40.b[0] (u_uart/U2/tx_data_dy_b[4]_syn_18) net  (fanout = 1)       0.594 r     5.060                    
 u_uart/U2/tx_data_dy_b[2]_syn_40.f[0]                       cell (LUT5)             0.431 r     5.491
 u_uart/U2/tx_data_dy_b[4]_syn_42.a[0] (u_uart/U2/tx_data_dy_b[4]_syn_20) net  (fanout = 1)       0.456 r     5.947      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/tx_data_dy_b[4]_syn_42                            path2reg0 (LUT5)        0.542       6.489
 Arrival time                                                                        6.489                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/tx_data_dy_b[4]_syn_42.clk (signal_process/clk)   net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.493ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.605 ns                                                        
 Start Point:             wendu/reg4_syn_109.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/tx_data_dy_b[4]_syn_42.b[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         2.931ns  (logic 1.381ns, net 1.550ns, 47% logic)                
 Logic Levels:            3 ( LUT5=3 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_109.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_109.q[0]                                     clk2q                   0.146 r     3.592
 u_uart/U2/tx_data_dy_b[4]_syn_44.d[1] (wendu/data[12])      net  (fanout = 3)       0.344 r     3.936      ../../Src_al/DS18B20.v(54)
 u_uart/U2/tx_data_dy_b[4]_syn_44.f[1]                       cell (LUT5)             0.262 r     4.198
 u_uart/U2/tx_data_dy_b[4]_syn_42.a[1] (u_uart/U2/tx_data_dy_b[4]_syn_2) net  (fanout = 1)       0.468 r     4.666                    
 u_uart/U2/tx_data_dy_b[4]_syn_42.f[1]                       cell (LUT5)             0.424 r     5.090
 u_uart/U2/tx_data_dy_b[4]_syn_42.b[0] (u_uart/U2/tx_data_dy_b[4]_syn_8) net  (fanout = 1)       0.738 r     5.828      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/tx_data_dy_b[4]_syn_42                            path2reg0 (LUT5)        0.549       6.377
 Arrival time                                                                        6.377                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/tx_data_dy_b[4]_syn_42.clk (signal_process/clk)   net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.605ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     0.637 ns                                                        
 Start Point:             wendu/reg4_syn_109.clk (rising edge triggered by clock clk_us)  
 End Point:               u_uart/U2/tx_data_dy_b[4]_syn_42.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         2.899ns  (logic 1.374ns, net 1.525ns, 47% logic)                
 Logic Levels:            3 ( LUT5=2 LUT4=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg4_syn_109.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg4_syn_109.q[0]                                     clk2q                   0.146 r     3.592
 u_uart/U2/tx_data_dy_b[4]_syn_48.d[1] (wendu/data[12])      net  (fanout = 3)       0.760 r     4.352      ../../Src_al/DS18B20.v(54)
 u_uart/U2/tx_data_dy_b[4]_syn_48.f[1]                       cell (LUT4)             0.262 r     4.614
 u_uart/U2/tx_data_dy_b[2]_syn_40.a[0] (u_uart/U2/tx_data_dy_b[4]_syn_14) net  (fanout = 1)       0.309 r     4.923                    
 u_uart/U2/tx_data_dy_b[2]_syn_40.f[0]                       cell (LUT5)             0.424 r     5.347
 u_uart/U2/tx_data_dy_b[4]_syn_42.a[0] (u_uart/U2/tx_data_dy_b[4]_syn_20) net  (fanout = 1)       0.456 r     5.803      ../../Src_al/Ctrl_Data.v(32)
 u_uart/U2/tx_data_dy_b[4]_syn_42                            path2reg0 (LUT5)        0.542       6.345
 Arrival time                                                                        6.345                  (3 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 u_uart/U2/tx_data_dy_b[4]_syn_42.clk (signal_process/clk)   net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  5.053       7.098
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116       6.982
 clock uncertainty                                                                  -0.000       6.982
 clock recovergence pessimism                                                        0.000       6.982
 Required time                                                                       6.982            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.637ns          

---------------------------------------------------------------------------------------------------------

Hold checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_30 (7 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.256 ns                                                        
 Start Point:             signal_process/demodu/fifo/reg0_syn_74.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[10] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.501ns  (logic 0.109ns, net 0.392ns, 21% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg0_syn_74.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/reg0_syn_74.q[1]                 clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[10] (signal_process/demodu/fifo/rd_addr[6]) net  (fanout = 9)       0.392 r     2.439      ../../al_ip/Asys_fifo56X16.v(67)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30         path2reg (EMB)          0.000       2.439
 Arrival time                                                                        2.439                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30.clkb (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.256ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.269 ns                                                        
 Start Point:             signal_process/demodu/fifo/reg0_syn_80.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[8] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.514ns  (logic 0.109ns, net 0.405ns, 21% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg0_syn_80.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/reg0_syn_80.q[0]                 clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[8] (signal_process/demodu/fifo/rd_addr[4]) net  (fanout = 9)       0.405 r     2.452      ../../al_ip/Asys_fifo56X16.v(67)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30         path2reg (EMB)          0.000       2.452
 Arrival time                                                                        2.452                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30.clkb (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.269ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.290 ns                                                        
 Start Point:             signal_process/demodu/fifo/reg0_syn_77.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[9] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.535ns  (logic 0.109ns, net 0.426ns, 20% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg0_syn_77.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/reg0_syn_77.q[0]                 clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[9] (signal_process/demodu/fifo/rd_addr[5]) net  (fanout = 9)       0.426 r     2.473      ../../al_ip/Asys_fifo56X16.v(67)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30         path2reg (EMB)          0.000       2.473
 Arrival time                                                                        2.473                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_30.clkb (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.290ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg9_syn_238 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.258 ns                                                        
 Start Point:             signal_process/demodu/reg11_syn_212.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg9_syn_238.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.420ns  (logic 0.204ns, net 0.216ns, 48% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_212.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg11_syn_212.q[1]                    clk2q                   0.109 r     2.138
 signal_process/demodu/reg9_syn_238.mi[1] (signal_process/demodu/sample_sum[43]) net  (fanout = 3)       0.216 r     2.354      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg9_syn_238                          path2reg1               0.095       2.449
 Arrival time                                                                        2.449                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg9_syn_238.clk (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       2.191
 clock uncertainty                                                                   0.000       2.191
 clock recovergence pessimism                                                        0.000       2.191
 Required time                                                                       2.191            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.258ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/rs422/reg4_syn_304 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.260 ns                                                        
 Start Point:             signal_process/integ/reg1_syn_237.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/rs422/reg4_syn_304.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.429ns  (logic 0.204ns, net 0.225ns, 47% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/integ/reg1_syn_237.clk (signal_process/clk)  net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/integ/reg1_syn_237.q[1]                      clk2q                   0.109 r     2.047
 signal_process/rs422/reg4_syn_304.mi[1] (signal_process/ang_vel_data[44]) net  (fanout = 1)       0.225 r     2.272      ../../Src_al/SignalProcessing.v(81)
 signal_process/rs422/reg4_syn_304                           path2reg1               0.095       2.367
 Arrival time                                                                        2.367                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/rs422/reg4_syn_304.clk (signal_process/clk)  net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       2.191
 clock uncertainty                                                                   0.000       2.191
 clock recovergence pessimism                                                       -0.084       2.107
 Required time                                                                       2.107            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.260ns          

---------------------------------------------------------------------------------------------------------

Recovery checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  6.630 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36.sr (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.339ns  (logic 0.232ns, net 1.107ns, 17% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.422
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36.sr (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 50)      1.107 r     3.529      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36  path2reg                0.086       3.615
 Arrival time                                                                        3.615                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      10.078
 clock uncertainty                                                                  -0.000      10.078
 clock recovergence pessimism                                                        0.167      10.245
 Required time                                                                      10.245            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.630ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  6.681 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.sr (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.288ns  (logic 0.232ns, net 1.056ns, 18% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.422
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.sr (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 50)      1.056 r     3.478      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31  path2reg                0.086       3.564
 Arrival time                                                                        3.564                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.clk (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      10.078
 clock uncertainty                                                                  -0.000      10.078
 clock recovergence pessimism                                                        0.167      10.245
 Required time                                                                      10.245            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.681ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_68 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  6.727 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_68.rstb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.292ns  (logic 0.146ns, net 1.146ns, 11% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.422
 signal_process/demodu/fifo/ram_inst/ramread0_syn_68.rstb (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 50)      1.146 r     3.568      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_68         path2reg                0.000       3.568
 Arrival time                                                                        3.568                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_68.clkb (signal_process/clk) net                     2.045       2.045      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  8.333      10.378
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.250      10.128
 clock uncertainty                                                                  -0.000      10.128
 clock recovergence pessimism                                                        0.167      10.295
 Required time                                                                      10.295            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.727ns          

---------------------------------------------------------------------------------------------------------

Removal checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/reg0_syn_80 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.127 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/reg0_syn_80.sr (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.409ns  (logic 0.161ns, net 0.248ns, 39% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/reg0_syn_80.sr (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 50)      0.248 r     2.295      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/reg0_syn_80                      path2reg                0.052       2.347
 Arrival time                                                                        2.347                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg0_syn_80.clk (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.383
 clock uncertainty                                                                   0.000       2.383
 clock recovergence pessimism                                                       -0.163       2.220
 Required time                                                                       2.220            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.127ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_11 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.221 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_11.rstb (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.516ns  (logic 0.109ns, net 0.407ns, 21% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.rstb (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 50)      0.407 r     2.454      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11         path2reg                0.000       2.454
 Arrival time                                                                        2.454                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.clkb (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.250       2.380
 clock uncertainty                                                                   0.000       2.380
 clock recovergence pessimism                                                       -0.147       2.233
 Required time                                                                       2.233            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.221ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.235 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42.sr (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.533ns  (logic 0.161ns, net 0.372ns, 30% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk (signal_process/clk) net                     1.938       1.938      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.047
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42.sr (signal_process/demodu/fifo/asy_r_rst1) net  (fanout = 50)      0.372 r     2.419      ../../al_ip/Asys_fifo56X16.v(61)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42  path2reg                0.052       2.471
 Arrival time                                                                        2.471                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42.clk (signal_process/clk) net                     2.130       2.130      ../../Src_al/SignalProcessing.v(61)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.383
 clock uncertainty                                                                   0.000       2.383
 clock recovergence pessimism                                                       -0.147       2.236
 Required time                                                                       2.236            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.235ns          

---------------------------------------------------------------------------------------------------------

Period checks:
---------------------------------------------------------------------------------------------------------
 Point                                             Type          Setting(ns)    Requied(ns)     Slack(ns)  
---------------------------------------------------------------------------------------------------------
 signal_process/modu/mult0_syn_2.clk               min period       8.333          3.414          4.919    

=========================================================================================================
Timing constraint:        clock: CLK120/pll_inst.clkc[3]                                  
Clock = CLK120/pll_inst.clkc[3], period 16.666ns, rising at 0ns, falling at 8.333ns

574 endpoints analyzed totally, and 5474 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Minimum period is 9.726ns
---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg0_syn_13 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     6.940 ns                                                        
 Start Point:             signal_process/demodu/reg7_syn_275.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/reg0_syn_13.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.167ns  (logic 0.289ns, net 0.878ns, 24% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/reg7_syn_275.clk (signal_process/clk) net                     2.276       2.276      ../../Src_al/SignalProcessing.v(61)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg7_syn_275.q[0]                     clk2q                   0.146 r     2.422
 signal_process/demodu/reg0_syn_13.mi[0] (signal_process/ctrl_signal/AD_valid) net  (fanout = 2)       0.878 r     3.300      ../../Src_al/SignalGenerator.v(52)
 signal_process/demodu/reg0_syn_13                           path2reg0               0.143       3.443
 Arrival time                                                                        3.443                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg0_syn_13.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  8.333      10.499
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      10.383
 clock uncertainty                                                                  -0.000      10.383
 clock recovergence pessimism                                                        0.000      10.383
 Required time                                                                      10.383            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               6.940ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg11_syn_215 (57 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     11.340 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_40.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_215.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.146ns  (logic 2.579ns, net 2.567ns, 50% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_40.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_40.q[1]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_324.e[1] (signal_process/demodu/din_reg1[6]) net  (fanout = 1)       1.507 r     4.063      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_324.fco                      cell (ADDER)            0.715 r     4.778
 signal_process/demodu/add1_syn_325.fci (signal_process/demodu/add1_syn_274) net  (fanout = 1)       0.000 f     4.778      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_325.fco                      cell (ADDER)            0.132 r     4.910
 signal_process/demodu/add1_syn_326.fci (signal_process/demodu/add1_syn_278) net  (fanout = 1)       0.000 f     4.910      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_326.fco                      cell (ADDER)            0.132 r     5.042
 signal_process/demodu/add1_syn_327.fci (signal_process/demodu/add1_syn_282) net  (fanout = 1)       0.000 f     5.042      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.132 r     5.174
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_286) net  (fanout = 1)       0.000 f     5.174      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     5.306
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_290) net  (fanout = 1)       0.000 f     5.306      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     5.438
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_294) net  (fanout = 1)       0.000 f     5.438      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     5.570
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_298) net  (fanout = 1)       0.000 f     5.570      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     5.702
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_302) net  (fanout = 1)       0.000 f     5.702      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     5.834
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_306) net  (fanout = 1)       0.000 f     5.834      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.966
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_310) net  (fanout = 1)       0.000 f     5.966      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fx[0]                    cell (ADDER)            0.387 r     6.353
 signal_process/demodu/reg11_syn_215.mi[1] (signal_process/demodu/sample_sum_b2[44]) net  (fanout = 1)       1.060 r     7.413      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_215                         path2reg1               0.143       7.556
 Arrival time                                                                        7.556                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_215.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.000      18.716
 clock recovergence pessimism                                                        0.180      18.896
 Required time                                                                      18.896            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.340ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.381 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_45.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_215.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.105ns  (logic 2.513ns, net 2.592ns, 49% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_45.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_45.q[0]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_325.e[0] (signal_process/demodu/din_reg1[8]) net  (fanout = 1)       1.532 r     4.088      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_325.fco                      cell (ADDER)            0.781 r     4.869
 signal_process/demodu/add1_syn_326.fci (signal_process/demodu/add1_syn_278) net  (fanout = 1)       0.000 f     4.869      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_326.fco                      cell (ADDER)            0.132 r     5.001
 signal_process/demodu/add1_syn_327.fci (signal_process/demodu/add1_syn_282) net  (fanout = 1)       0.000 f     5.001      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.132 r     5.133
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_286) net  (fanout = 1)       0.000 f     5.133      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     5.265
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_290) net  (fanout = 1)       0.000 f     5.265      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     5.397
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_294) net  (fanout = 1)       0.000 f     5.397      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     5.529
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_298) net  (fanout = 1)       0.000 f     5.529      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     5.661
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_302) net  (fanout = 1)       0.000 f     5.661      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     5.793
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_306) net  (fanout = 1)       0.000 f     5.793      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.925
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_310) net  (fanout = 1)       0.000 f     5.925      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fx[0]                    cell (ADDER)            0.387 r     6.312
 signal_process/demodu/reg11_syn_215.mi[1] (signal_process/demodu/sample_sum_b2[44]) net  (fanout = 1)       1.060 r     7.372      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_215                         path2reg1               0.143       7.515
 Arrival time                                                                        7.515                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_215.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.000      18.716
 clock recovergence pessimism                                                        0.180      18.896
 Required time                                                                      18.896            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.381ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.533 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_47.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_215.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.953ns  (logic 2.513ns, net 2.440ns, 50% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_47.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_47.q[0]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_325.d[0] (signal_process/demodu/din_reg1[7]) net  (fanout = 1)       1.380 r     3.936      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_325.fco                      cell (ADDER)            0.781 r     4.717
 signal_process/demodu/add1_syn_326.fci (signal_process/demodu/add1_syn_278) net  (fanout = 1)       0.000 f     4.717      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_326.fco                      cell (ADDER)            0.132 r     4.849
 signal_process/demodu/add1_syn_327.fci (signal_process/demodu/add1_syn_282) net  (fanout = 1)       0.000 f     4.849      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.132 r     4.981
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_286) net  (fanout = 1)       0.000 f     4.981      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     5.113
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_290) net  (fanout = 1)       0.000 f     5.113      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     5.245
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_294) net  (fanout = 1)       0.000 f     5.245      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     5.377
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_298) net  (fanout = 1)       0.000 f     5.377      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     5.509
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_302) net  (fanout = 1)       0.000 f     5.509      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     5.641
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_306) net  (fanout = 1)       0.000 f     5.641      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.773
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_310) net  (fanout = 1)       0.000 f     5.773      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fx[0]                    cell (ADDER)            0.387 r     6.160
 signal_process/demodu/reg11_syn_215.mi[1] (signal_process/demodu/sample_sum_b2[44]) net  (fanout = 1)       1.060 r     7.220      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_215                         path2reg1               0.143       7.363
 Arrival time                                                                        7.363                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_215.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.000      18.716
 clock recovergence pessimism                                                        0.180      18.896
 Required time                                                                      18.896            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.533ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/reg11_syn_203 (65 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     11.392 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_40.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_203.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.094ns  (logic 2.843ns, net 2.251ns, 55% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_40.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_40.q[1]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_324.e[1] (signal_process/demodu/din_reg1[6]) net  (fanout = 1)       1.507 r     4.063      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_324.fco                      cell (ADDER)            0.715 r     4.778
 signal_process/demodu/add1_syn_325.fci (signal_process/demodu/add1_syn_274) net  (fanout = 1)       0.000 f     4.778      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_325.fco                      cell (ADDER)            0.132 r     4.910
 signal_process/demodu/add1_syn_326.fci (signal_process/demodu/add1_syn_278) net  (fanout = 1)       0.000 f     4.910      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_326.fco                      cell (ADDER)            0.132 r     5.042
 signal_process/demodu/add1_syn_327.fci (signal_process/demodu/add1_syn_282) net  (fanout = 1)       0.000 f     5.042      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.132 r     5.174
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_286) net  (fanout = 1)       0.000 f     5.174      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     5.306
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_290) net  (fanout = 1)       0.000 f     5.306      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     5.438
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_294) net  (fanout = 1)       0.000 f     5.438      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     5.570
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_298) net  (fanout = 1)       0.000 f     5.570      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     5.702
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_302) net  (fanout = 1)       0.000 f     5.702      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     5.834
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_306) net  (fanout = 1)       0.000 f     5.834      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.966
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_310) net  (fanout = 1)       0.000 f     5.966      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     6.098
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_314) net  (fanout = 1)       0.000 f     6.098      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     6.230
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_318) net  (fanout = 1)       0.000 f     6.230      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fx[0]                    cell (ADDER)            0.387 r     6.617
 signal_process/demodu/reg11_syn_203.mi[1] (signal_process/demodu/sample_sum_b2[52]) net  (fanout = 1)       0.744 r     7.361      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_203                         path2reg1               0.143       7.504
 Arrival time                                                                        7.504                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_203.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.000      18.716
 clock recovergence pessimism                                                        0.180      18.896
 Required time                                                                      18.896            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.392ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.433 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_45.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_203.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.053ns  (logic 2.777ns, net 2.276ns, 54% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_45.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_45.q[0]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_325.e[0] (signal_process/demodu/din_reg1[8]) net  (fanout = 1)       1.532 r     4.088      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_325.fco                      cell (ADDER)            0.781 r     4.869
 signal_process/demodu/add1_syn_326.fci (signal_process/demodu/add1_syn_278) net  (fanout = 1)       0.000 f     4.869      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_326.fco                      cell (ADDER)            0.132 r     5.001
 signal_process/demodu/add1_syn_327.fci (signal_process/demodu/add1_syn_282) net  (fanout = 1)       0.000 f     5.001      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.132 r     5.133
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_286) net  (fanout = 1)       0.000 f     5.133      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     5.265
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_290) net  (fanout = 1)       0.000 f     5.265      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     5.397
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_294) net  (fanout = 1)       0.000 f     5.397      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     5.529
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_298) net  (fanout = 1)       0.000 f     5.529      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     5.661
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_302) net  (fanout = 1)       0.000 f     5.661      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     5.793
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_306) net  (fanout = 1)       0.000 f     5.793      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.925
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_310) net  (fanout = 1)       0.000 f     5.925      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     6.057
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_314) net  (fanout = 1)       0.000 f     6.057      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     6.189
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_318) net  (fanout = 1)       0.000 f     6.189      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fx[0]                    cell (ADDER)            0.387 r     6.576
 signal_process/demodu/reg11_syn_203.mi[1] (signal_process/demodu/sample_sum_b2[52]) net  (fanout = 1)       0.744 r     7.320      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_203                         path2reg1               0.143       7.463
 Arrival time                                                                        7.463                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_203.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.000      18.716
 clock recovergence pessimism                                                        0.180      18.896
 Required time                                                                      18.896            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.433ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     11.585 ns                                                       
 Start Point:             signal_process/demodu/reg6_syn_47.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/reg11_syn_203.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         4.901ns  (logic 2.777ns, net 2.124ns, 56% logic)                
 Logic Levels:            2 ( ADDER=2 )                                                   

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg6_syn_47.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg6_syn_47.q[0]                      clk2q                   0.146 r     2.556
 signal_process/demodu/add1_syn_325.d[0] (signal_process/demodu/din_reg1[7]) net  (fanout = 1)       1.380 r     3.936      ../../Src_al/Demodulation.v(64)
 signal_process/demodu/add1_syn_325.fco                      cell (ADDER)            0.781 r     4.717
 signal_process/demodu/add1_syn_326.fci (signal_process/demodu/add1_syn_278) net  (fanout = 1)       0.000 f     4.717      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_326.fco                      cell (ADDER)            0.132 r     4.849
 signal_process/demodu/add1_syn_327.fci (signal_process/demodu/add1_syn_282) net  (fanout = 1)       0.000 f     4.849      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_327.fco                      cell (ADDER)            0.132 r     4.981
 signal_process/demodu/add1_syn_328.fci (signal_process/demodu/add1_syn_286) net  (fanout = 1)       0.000 f     4.981      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_328.fco                      cell (ADDER)            0.132 r     5.113
 signal_process/demodu/add1_syn_329.fci (signal_process/demodu/add1_syn_290) net  (fanout = 1)       0.000 f     5.113      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_329.fco                      cell (ADDER)            0.132 r     5.245
 signal_process/demodu/add1_syn_330.fci (signal_process/demodu/add1_syn_294) net  (fanout = 1)       0.000 f     5.245      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_330.fco                      cell (ADDER)            0.132 r     5.377
 signal_process/demodu/add1_syn_331.fci (signal_process/demodu/add1_syn_298) net  (fanout = 1)       0.000 f     5.377      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_331.fco                      cell (ADDER)            0.132 r     5.509
 signal_process/demodu/add1_syn_332.fci (signal_process/demodu/add1_syn_302) net  (fanout = 1)       0.000 f     5.509      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_332.fco                      cell (ADDER)            0.132 r     5.641
 signal_process/demodu/add1_syn_333.fci (signal_process/demodu/add1_syn_306) net  (fanout = 1)       0.000 f     5.641      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_333.fco                      cell (ADDER)            0.132 r     5.773
 signal_process/demodu/add1_syn_334.fci (signal_process/demodu/add1_syn_310) net  (fanout = 1)       0.000 f     5.773      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_334.fco                      cell (ADDER)            0.132 r     5.905
 signal_process/demodu/add1_syn_335.fci (signal_process/demodu/add1_syn_314) net  (fanout = 1)       0.000 f     5.905      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_335.fco                      cell (ADDER)            0.132 r     6.037
 signal_process/demodu/add1_syn_336.fci (signal_process/demodu/add1_syn_318) net  (fanout = 1)       0.000 f     6.037      ../../Src_al/Demodulation.v(113)
 signal_process/demodu/add1_syn_336.fx[0]                    cell (ADDER)            0.387 r     6.424
 signal_process/demodu/reg11_syn_203.mi[1] (signal_process/demodu/sample_sum_b2[52]) net  (fanout = 1)       0.744 r     7.168      ../../Src_al/Demodulation.v(65)
 signal_process/demodu/reg11_syn_203                         path2reg1               0.143       7.311
 Arrival time                                                                        7.311                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg11_syn_203.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      18.716
 clock uncertainty                                                                  -0.000      18.716
 clock recovergence pessimism                                                        0.180      18.896
 Required time                                                                      18.896            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              11.585ns          

---------------------------------------------------------------------------------------------------------

Hold checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_49 (9 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.089 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_215.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.334ns  (logic 0.109ns, net 0.225ns, 32% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_215.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_215.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] (signal_process/demodu/latch_sample_sum[43]) net  (fanout = 1)       0.225 r     2.363      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49         path2reg (EMB)          0.000       2.363
 Arrival time                                                                        2.363                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.089ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.196 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_218.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.441ns  (logic 0.109ns, net 0.332ns, 24% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_218.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_218.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] (signal_process/demodu/latch_sample_sum[44]) net  (fanout = 1)       0.332 r     2.470      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49         path2reg (EMB)          0.000       2.470
 Arrival time                                                                        2.470                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.196ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.205 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_221.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.450ns  (logic 0.109ns, net 0.341ns, 24% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_221.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_221.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] (signal_process/demodu/latch_sample_sum[39]) net  (fanout = 1)       0.341 r     2.479      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49         path2reg (EMB)          0.000       2.479
 Arrival time                                                                        2.479                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_49.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.205ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_11 (9 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.089 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_179.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.334ns  (logic 0.109ns, net 0.225ns, 32% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_179.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_179.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] (signal_process/demodu/latch_sample_sum[16]) net  (fanout = 1)       0.225 r     2.363      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11         path2reg (EMB)          0.000       2.363
 Arrival time                                                                        2.363                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.089ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.114 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_176.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.359ns  (logic 0.109ns, net 0.250ns, 30% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_176.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_176.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] (signal_process/demodu/latch_sample_sum[17]) net  (fanout = 1)       0.250 r     2.388      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11         path2reg (EMB)          0.000       2.388
 Arrival time                                                                        2.388                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.114ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.186 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_191.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.431ns  (logic 0.109ns, net 0.322ns, 25% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_191.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_191.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] (signal_process/demodu/latch_sample_sum[9]) net  (fanout = 1)       0.322 r     2.460      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11         path2reg (EMB)          0.000       2.460
 Arrival time                                                                        2.460                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.186ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/ram_inst/ramread0_syn_11 (9 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.089 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_197.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.334ns  (logic 0.109ns, net 0.225ns, 32% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_197.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_197.q[0]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] (signal_process/demodu/latch_sample_sum[5]) net  (fanout = 1)       0.225 r     2.363      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11         path2reg (EMB)          0.000       2.363
 Arrival time                                                                        2.363                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.089ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.089 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_191.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.334ns  (logic 0.109ns, net 0.225ns, 32% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_191.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_191.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] (signal_process/demodu/latch_sample_sum[3]) net  (fanout = 1)       0.225 r     2.363      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11         path2reg (EMB)          0.000       2.363
 Arrival time                                                                        2.363                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.089ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.089 ns                                                        
 Start Point:             signal_process/demodu/reg8_syn_200.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.334ns  (logic 0.109ns, net 0.225ns, 32% logic)                
 Logic Levels:            1 ( EMB=1 )                                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/reg8_syn_200.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/reg8_syn_200.q[1]                     clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] (signal_process/demodu/latch_sample_sum[1]) net  (fanout = 1)       0.225 r     2.363      ../../Src_al/Demodulation.v(66)
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11         path2reg (EMB)          0.000       2.363
 Arrival time                                                                        2.363                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.clka (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.430
 clock uncertainty                                                                   0.000       2.430
 clock recovergence pessimism                                                       -0.156       2.274
 Required time                                                                       2.274            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.089ns          

---------------------------------------------------------------------------------------------------------

Recovery checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/full_flag_reg_syn_5 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  15.046 ns                                                       
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/full_flag_reg_syn_5.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.256ns  (logic 0.232ns, net 1.024ns, 18% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.556
 signal_process/demodu/fifo/full_flag_reg_syn_5.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 19)      1.024 r     3.580      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/full_flag_reg_syn_5              path2reg                0.086       3.666
 Arrival time                                                                        3.666                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/full_flag_reg_syn_5.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      18.532
 clock uncertainty                                                                  -0.000      18.532
 clock recovergence pessimism                                                        0.180      18.712
 Required time                                                                      18.712            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              15.046ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  15.051 ns                                                       
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.251ns  (logic 0.232ns, net 1.019ns, 18% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.556
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 19)      1.019 r     3.575      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26  path2reg                0.086       3.661
 Arrival time                                                                        3.661                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      18.532
 clock uncertainty                                                                  -0.000      18.532
 clock recovergence pessimism                                                        0.180      18.712
 Required time                                                                      18.712            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              15.051ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (recovery check):  15.188 ns                                                       
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         1.114ns  (logic 0.232ns, net 0.882ns, 20% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.410       2.410      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.410
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.146 r     2.556
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 19)      0.882 r     3.438      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39  path2reg                0.086       3.524
 Arrival time                                                                        3.524                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk (signal_process/demodu/clk_in) net                     2.166       2.166      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.832
---------------------------------------------------------------------------------------------------------
 cell recovery                                                                      -0.300      18.532
 clock uncertainty                                                                  -0.000      18.532
 clock recovergence pessimism                                                        0.180      18.712
 Required time                                                                      18.712            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              15.188ns          

---------------------------------------------------------------------------------------------------------

Removal checks:
---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.246 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.544ns  (logic 0.161ns, net 0.383ns, 29% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 19)      0.383 r     2.521      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36  path2reg                0.052       2.573
 Arrival time                                                                        2.573                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.483
 clock uncertainty                                                                   0.000       2.483
 clock recovergence pessimism                                                       -0.156       2.327
 Required time                                                                       2.327            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.246ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.285 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.583ns  (logic 0.161ns, net 0.422ns, 27% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 19)      0.422 r     2.560      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36  path2reg                0.052       2.612
 Arrival time                                                                        2.612                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.clk (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.483
 clock uncertainty                                                                   0.000       2.483
 clock recovergence pessimism                                                       -0.156       2.327
 Required time                                                                       2.327            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.285ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/reg1_syn_75 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (removal check):   0.285 ns                                                        
 Start Point:             signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/reg1_syn_75.sr (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.583ns  (logic 0.161ns, net 0.422ns, 27% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk (signal_process/demodu/clk_in) net                     2.029       2.029      ../../Src_al/Demodulation.v(51)
 launch clock edge                                                                   0.000       2.029
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.q[1]        clk2q                   0.109 r     2.138
 signal_process/demodu/fifo/reg1_syn_75.sr (signal_process/demodu/fifo/asy_w_rst1) net  (fanout = 19)      0.422 r     2.560      ../../al_ip/Asys_fifo56X16.v(59)
 signal_process/demodu/fifo/reg1_syn_75                      path2reg                0.052       2.612
 Arrival time                                                                        2.612                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/reg1_syn_75.clk (signal_process/demodu/clk_in) net                     2.230       2.230      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.230
---------------------------------------------------------------------------------------------------------
 cell removal                                                                        0.253       2.483
 clock uncertainty                                                                   0.000       2.483
 clock recovergence pessimism                                                       -0.156       2.327
 Required time                                                                       2.327            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.285ns          

---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        clock: CLK120/pll_inst.clkc[4]                                  
Clock = CLK120/pll_inst.clkc[4], period 16.666ns, rising at 0ns, falling at 8.333ns

0 endpoints analyzed totally, and 0 paths analyzed
0 errors detected : 0 setup errors (TNS = 0), 0 hold errors (TNS = 0)
Minimum period is 0ns
---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        clock: clk_us                                                   
Clock = clk_us, period 1000ns, rising at 0ns, falling at 500ns

198 endpoints analyzed totally, and 7662 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Minimum period is 7.625ns
---------------------------------------------------------------------------------------------------------

Paths for end point dq_syn_2 (131 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     992.375 ns                                                      
 Start Point:             wendu/reg2_syn_129.clk (rising edge triggered by clock clk_us)  
 End Point:               dq_syn_2.ts (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.373ns  (logic 2.682ns, net 4.691ns, 36% logic)                
 Logic Levels:            5 ( LUT4=2 ADDER=2 LUT5=1 )                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_129.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_129.q[0]                                     clk2q                   0.146 r     3.592
 wendu/lt0_syn_90.a[1] (wendu/cnt_us[3])                     net  (fanout = 10)      0.981 r     4.573      ../../Src_al/DS18B20.v(51)
 wendu/lt0_syn_90.fco                                        cell (ADDER)            0.627 r     5.200
 wendu/lt0_syn_93.fci (wendu/lt0_syn_10)                     net  (fanout = 1)       0.000 f     5.200      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_93.fco                                        cell (ADDER)            0.073 r     5.273
 wendu/lt0_syn_96.fci (wendu/lt0_syn_14)                     net  (fanout = 1)       0.000 f     5.273      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_96.fco                                        cell (ADDER)            0.073 r     5.346
 wendu/lt0_syn_99.fci (wendu/lt0_syn_18)                     net  (fanout = 1)       0.000 f     5.346      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_99.fco                                        cell (ADDER)            0.073 r     5.419
 wendu/lt0_syn_102.fci (wendu/lt0_syn_22)                    net  (fanout = 1)       0.000 f     5.419      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_102.fco                                       cell (ADDER)            0.073 r     5.492
 wendu/lt0_syn_105.fci (wendu/lt0_syn_26)                    net  (fanout = 1)       0.000 f     5.492      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_105.fco                                       cell (ADDER)            0.073 r     5.565
 wendu/lt0_syn_108.fci (wendu/lt0_syn_30)                    net  (fanout = 1)       0.000 f     5.565      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_108.fco                                       cell (ADDER)            0.073 r     5.638
 wendu/lt0_syn_111.fci (wendu/lt0_syn_34)                    net  (fanout = 1)       0.000 f     5.638      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_111.fco                                       cell (ADDER)            0.073 r     5.711
 wendu/lt0_syn_114.fci (wendu/lt0_syn_38)                    net  (fanout = 1)       0.000 f     5.711      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_114.fco                                       cell (ADDER)            0.073 r     5.784
 wendu/lt0_syn_116.fci (wendu/lt0_syn_42)                    net  (fanout = 1)       0.000 f     5.784      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_116.f[0]                                      cell (ADDER)            0.144 r     5.928
 wendu/next_state[0]_syn_48.a[0] (wendu/data_temp_b2_n)      net  (fanout = 1)       0.676 r     6.604                    
 wendu/next_state[0]_syn_48.f[0]                             cell (LUT5)             0.424 r     7.028
 wendu/cur_state[0]_syn_3761.a[1] (wendu/cur_state[0]_syn_3584) net  (fanout = 1)       0.738 r     7.766      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3761.f[1]                            cell (LUT4)             0.424 r     8.190
 wendu/cur_state[0]_syn_3769.b[0] (wendu/cur_state[0]_syn_3586) net  (fanout = 1)       0.309 r     8.499      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3769.f[0]                            cell (LUT4)             0.333 r     8.832
 dq_syn_2.ts (wendu/cur_state[0]_syn_4)                      net  (fanout = 1)       1.987 r    10.819      ../../Src_al/DS18B20.v(44)
 dq_syn_2                                                    path2reg                0.000      10.819
 Arrival time                                                                       10.819                  (5 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.907       2.907      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.907
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.000    1002.907
 clock uncertainty                                                                  -0.000    1002.907
 clock recovergence pessimism                                                        0.287    1003.194
 Required time                                                                    1003.194            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             992.375ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     992.416 ns                                                      
 Start Point:             wendu/reg2_syn_142.clk (rising edge triggered by clock clk_us)  
 End Point:               dq_syn_2.ts (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.332ns  (logic 2.688ns, net 4.644ns, 36% logic)                
 Logic Levels:            5 ( LUT4=2 ADDER=2 LUT5=1 )                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_142.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_142.q[0]                                     clk2q                   0.146 r     3.592
 wendu/lt0_syn_93.a[0] (wendu/cnt_us[4])                     net  (fanout = 9)       0.934 r     4.526      ../../Src_al/DS18B20.v(51)
 wendu/lt0_syn_93.fco                                        cell (ADDER)            0.706 r     5.232
 wendu/lt0_syn_96.fci (wendu/lt0_syn_14)                     net  (fanout = 1)       0.000 f     5.232      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_96.fco                                        cell (ADDER)            0.073 r     5.305
 wendu/lt0_syn_99.fci (wendu/lt0_syn_18)                     net  (fanout = 1)       0.000 f     5.305      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_99.fco                                        cell (ADDER)            0.073 r     5.378
 wendu/lt0_syn_102.fci (wendu/lt0_syn_22)                    net  (fanout = 1)       0.000 f     5.378      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_102.fco                                       cell (ADDER)            0.073 r     5.451
 wendu/lt0_syn_105.fci (wendu/lt0_syn_26)                    net  (fanout = 1)       0.000 f     5.451      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_105.fco                                       cell (ADDER)            0.073 r     5.524
 wendu/lt0_syn_108.fci (wendu/lt0_syn_30)                    net  (fanout = 1)       0.000 f     5.524      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_108.fco                                       cell (ADDER)            0.073 r     5.597
 wendu/lt0_syn_111.fci (wendu/lt0_syn_34)                    net  (fanout = 1)       0.000 f     5.597      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_111.fco                                       cell (ADDER)            0.073 r     5.670
 wendu/lt0_syn_114.fci (wendu/lt0_syn_38)                    net  (fanout = 1)       0.000 f     5.670      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_114.fco                                       cell (ADDER)            0.073 r     5.743
 wendu/lt0_syn_116.fci (wendu/lt0_syn_42)                    net  (fanout = 1)       0.000 f     5.743      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_116.f[0]                                      cell (ADDER)            0.144 r     5.887
 wendu/next_state[0]_syn_48.a[0] (wendu/data_temp_b2_n)      net  (fanout = 1)       0.676 r     6.563                    
 wendu/next_state[0]_syn_48.f[0]                             cell (LUT5)             0.424 r     6.987
 wendu/cur_state[0]_syn_3761.a[1] (wendu/cur_state[0]_syn_3584) net  (fanout = 1)       0.738 r     7.725      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3761.f[1]                            cell (LUT4)             0.424 r     8.149
 wendu/cur_state[0]_syn_3769.b[0] (wendu/cur_state[0]_syn_3586) net  (fanout = 1)       0.309 r     8.458      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3769.f[0]                            cell (LUT4)             0.333 r     8.791
 dq_syn_2.ts (wendu/cur_state[0]_syn_4)                      net  (fanout = 1)       1.987 r    10.778      ../../Src_al/DS18B20.v(44)
 dq_syn_2                                                    path2reg                0.000      10.778
 Arrival time                                                                       10.778                  (5 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.907       2.907      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.907
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.000    1002.907
 clock uncertainty                                                                  -0.000    1002.907
 clock recovergence pessimism                                                        0.287    1003.194
 Required time                                                                    1003.194            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             992.416ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     992.490 ns                                                      
 Start Point:             wendu/reg2_syn_145.clk (rising edge triggered by clock clk_us)  
 End Point:               dq_syn_2.ts (rising edge triggered by clock clk_us)             
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.258ns  (logic 2.761ns, net 4.497ns, 38% logic)                
 Logic Levels:            5 ( LUT4=2 ADDER=2 LUT5=1 )                                     

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_145.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_145.q[0]                                     clk2q                   0.146 r     3.592
 wendu/lt0_syn_90.a[0] (wendu/cnt_us[2])                     net  (fanout = 10)      0.787 r     4.379      ../../Src_al/DS18B20.v(51)
 wendu/lt0_syn_90.fco                                        cell (ADDER)            0.706 r     5.085
 wendu/lt0_syn_93.fci (wendu/lt0_syn_10)                     net  (fanout = 1)       0.000 f     5.085      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_93.fco                                        cell (ADDER)            0.073 r     5.158
 wendu/lt0_syn_96.fci (wendu/lt0_syn_14)                     net  (fanout = 1)       0.000 f     5.158      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_96.fco                                        cell (ADDER)            0.073 r     5.231
 wendu/lt0_syn_99.fci (wendu/lt0_syn_18)                     net  (fanout = 1)       0.000 f     5.231      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_99.fco                                        cell (ADDER)            0.073 r     5.304
 wendu/lt0_syn_102.fci (wendu/lt0_syn_22)                    net  (fanout = 1)       0.000 f     5.304      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_102.fco                                       cell (ADDER)            0.073 r     5.377
 wendu/lt0_syn_105.fci (wendu/lt0_syn_26)                    net  (fanout = 1)       0.000 f     5.377      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_105.fco                                       cell (ADDER)            0.073 r     5.450
 wendu/lt0_syn_108.fci (wendu/lt0_syn_30)                    net  (fanout = 1)       0.000 f     5.450      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_108.fco                                       cell (ADDER)            0.073 r     5.523
 wendu/lt0_syn_111.fci (wendu/lt0_syn_34)                    net  (fanout = 1)       0.000 f     5.523      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_111.fco                                       cell (ADDER)            0.073 r     5.596
 wendu/lt0_syn_114.fci (wendu/lt0_syn_38)                    net  (fanout = 1)       0.000 f     5.596      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_114.fco                                       cell (ADDER)            0.073 r     5.669
 wendu/lt0_syn_116.fci (wendu/lt0_syn_42)                    net  (fanout = 1)       0.000 f     5.669      ../../Src_al/DS18B20.v(298)
 wendu/lt0_syn_116.f[0]                                      cell (ADDER)            0.144 r     5.813
 wendu/next_state[0]_syn_48.a[0] (wendu/data_temp_b2_n)      net  (fanout = 1)       0.676 r     6.489                    
 wendu/next_state[0]_syn_48.f[0]                             cell (LUT5)             0.424 r     6.913
 wendu/cur_state[0]_syn_3761.a[1] (wendu/cur_state[0]_syn_3584) net  (fanout = 1)       0.738 r     7.651      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3761.f[1]                            cell (LUT4)             0.424 r     8.075
 wendu/cur_state[0]_syn_3769.b[0] (wendu/cur_state[0]_syn_3586) net  (fanout = 1)       0.309 r     8.384      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3769.f[0]                            cell (LUT4)             0.333 r     8.717
 dq_syn_2.ts (wendu/cur_state[0]_syn_4)                      net  (fanout = 1)       1.987 r    10.704      ../../Src_al/DS18B20.v(44)
 dq_syn_2                                                    path2reg                0.000      10.704
 Arrival time                                                                       10.704                  (5 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 dq_syn_2.osclk (wendu/clk_us_syn_4)                         net                     2.907       2.907      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1002.907
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.000    1002.907
 clock uncertainty                                                                  -0.000    1002.907
 clock recovergence pessimism                                                        0.287    1003.194
 Required time                                                                    1003.194            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             992.490ns          

---------------------------------------------------------------------------------------------------------

Paths for end point wendu/reg2_syn_129 (44 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     993.726 ns                                                      
 Start Point:             wendu/reg2_syn_139.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg2_syn_129.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.094ns  (logic 2.221ns, net 3.873ns, 36% logic)                
 Logic Levels:            6 ( LUT4=3 LUT2=2 LUT5=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_139.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_139.q[0]                                     clk2q                   0.146 r     3.592
 wendu/cur_state[0]_syn_3786.c[1] (wendu/cnt_us[10])         net  (fanout = 6)       0.871 r     4.463      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3786.f[1]                            cell (LUT4)             0.348 r     4.811
 wendu/cur_state[0]_syn_3756.a[0] (wendu/cur_state[0]_syn_3523) net  (fanout = 1)       0.594 r     5.405      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3756.f[0]                            cell (LUT5)             0.424 r     5.829
 wendu/cur_state[0]_syn_3792.a[1] (wendu/cur_state[0]_syn_3529) net  (fanout = 1)       0.456 r     6.285      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3792.f[1]                            cell (LUT4)             0.408 r     6.693
 wendu/cur_state[0]_syn_3812.d[1] (wendu/cur_state[0]_syn_3531) net  (fanout = 2)       0.738 r     7.431      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3812.f[1]                            cell (LUT2)             0.262 r     7.693
 wendu/cur_state[0]_syn_3789.d[1] (wendu/cur_state[2]_syn_1562) net  (fanout = 4)       0.601 r     8.294      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3789.f[1]                            cell (LUT4)             0.262 r     8.556
 wendu/reg2_syn_129.d[1] (wendu/cur_state[0]_syn_3534)       net  (fanout = 7)       0.613 r     9.169      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_129                                          path2reg1 (LUT2)        0.371       9.540
 Arrival time                                                                        9.540                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_129.clk (wendu/clk_us_syn_4)                 net                     3.095       3.095      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.095
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1002.979
 clock uncertainty                                                                  -0.000    1002.979
 clock recovergence pessimism                                                        0.287    1003.266
 Required time                                                                    1003.266            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.726ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     993.950 ns                                                      
 Start Point:             wendu/reg2_syn_142.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg2_syn_129.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.870ns  (logic 2.297ns, net 3.573ns, 39% logic)                
 Logic Levels:            6 ( LUT4=3 LUT2=2 LUT5=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_142.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_142.q[0]                                     clk2q                   0.146 r     3.592
 wendu/cur_state[0]_syn_3786.a[1] (wendu/cnt_us[4])          net  (fanout = 9)       0.571 r     4.163      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3786.f[1]                            cell (LUT4)             0.424 r     4.587
 wendu/cur_state[0]_syn_3756.a[0] (wendu/cur_state[0]_syn_3523) net  (fanout = 1)       0.594 r     5.181      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3756.f[0]                            cell (LUT5)             0.424 r     5.605
 wendu/cur_state[0]_syn_3792.a[1] (wendu/cur_state[0]_syn_3529) net  (fanout = 1)       0.456 r     6.061      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3792.f[1]                            cell (LUT4)             0.408 r     6.469
 wendu/cur_state[0]_syn_3812.d[1] (wendu/cur_state[0]_syn_3531) net  (fanout = 2)       0.738 r     7.207      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3812.f[1]                            cell (LUT2)             0.262 r     7.469
 wendu/cur_state[0]_syn_3789.d[1] (wendu/cur_state[2]_syn_1562) net  (fanout = 4)       0.601 r     8.070      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3789.f[1]                            cell (LUT4)             0.262 r     8.332
 wendu/reg2_syn_129.d[1] (wendu/cur_state[0]_syn_3534)       net  (fanout = 7)       0.613 r     8.945      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_129                                          path2reg1 (LUT2)        0.371       9.316
 Arrival time                                                                        9.316                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_129.clk (wendu/clk_us_syn_4)                 net                     3.095       3.095      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.095
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1002.979
 clock uncertainty                                                                  -0.000    1002.979
 clock recovergence pessimism                                                        0.287    1003.266
 Required time                                                                    1003.266            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.950ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     994.198 ns                                                      
 Start Point:             wendu/reg2_syn_148.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg2_syn_129.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.650ns  (logic 2.304ns, net 3.346ns, 40% logic)                
 Logic Levels:            6 ( LUT4=3 LUT2=2 LUT5=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_148.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_148.q[1]                                     clk2q                   0.146 r     3.592
 wendu/cur_state[0]_syn_3786.b[1] (wendu/cnt_us[7])          net  (fanout = 9)       0.344 r     3.936      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3786.f[1]                            cell (LUT4)             0.431 r     4.367
 wendu/cur_state[0]_syn_3756.a[0] (wendu/cur_state[0]_syn_3523) net  (fanout = 1)       0.594 r     4.961      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3756.f[0]                            cell (LUT5)             0.424 r     5.385
 wendu/cur_state[0]_syn_3792.a[1] (wendu/cur_state[0]_syn_3529) net  (fanout = 1)       0.456 r     5.841      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3792.f[1]                            cell (LUT4)             0.408 r     6.249
 wendu/cur_state[0]_syn_3812.d[1] (wendu/cur_state[0]_syn_3531) net  (fanout = 2)       0.738 r     6.987      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3812.f[1]                            cell (LUT2)             0.262 r     7.249
 wendu/cur_state[0]_syn_3789.d[1] (wendu/cur_state[2]_syn_1562) net  (fanout = 4)       0.601 r     7.850      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3789.f[1]                            cell (LUT4)             0.262 r     8.112
 wendu/reg2_syn_129.d[1] (wendu/cur_state[0]_syn_3534)       net  (fanout = 7)       0.613 r     8.725      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_129                                          path2reg1 (LUT2)        0.371       9.096
 Arrival time                                                                        9.096                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_129.clk (wendu/clk_us_syn_4)                 net                     3.095       3.095      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.095
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1002.979
 clock uncertainty                                                                  -0.000    1002.979
 clock recovergence pessimism                                                        0.315    1003.294
 Required time                                                                    1003.294            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             994.198ns          

---------------------------------------------------------------------------------------------------------

Paths for end point wendu/reg2_syn_135 (41 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     993.726 ns                                                      
 Start Point:             wendu/reg2_syn_139.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg2_syn_135.d[0] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.094ns  (logic 2.221ns, net 3.873ns, 36% logic)                
 Logic Levels:            6 ( LUT4=3 LUT2=2 LUT5=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_139.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_139.q[0]                                     clk2q                   0.146 r     3.592
 wendu/cur_state[0]_syn_3786.c[1] (wendu/cnt_us[10])         net  (fanout = 6)       0.871 r     4.463      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3786.f[1]                            cell (LUT4)             0.348 r     4.811
 wendu/cur_state[0]_syn_3756.a[0] (wendu/cur_state[0]_syn_3523) net  (fanout = 1)       0.594 r     5.405      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3756.f[0]                            cell (LUT5)             0.424 r     5.829
 wendu/cur_state[0]_syn_3792.a[1] (wendu/cur_state[0]_syn_3529) net  (fanout = 1)       0.456 r     6.285      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3792.f[1]                            cell (LUT4)             0.408 r     6.693
 wendu/cur_state[0]_syn_3812.d[1] (wendu/cur_state[0]_syn_3531) net  (fanout = 2)       0.738 r     7.431      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3812.f[1]                            cell (LUT2)             0.262 r     7.693
 wendu/cur_state[0]_syn_3789.d[1] (wendu/cur_state[2]_syn_1562) net  (fanout = 4)       0.601 r     8.294      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3789.f[1]                            cell (LUT4)             0.262 r     8.556
 wendu/reg2_syn_135.d[0] (wendu/cur_state[0]_syn_3534)       net  (fanout = 7)       0.613 r     9.169      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_135                                          path2reg0 (LUT2)        0.371       9.540
 Arrival time                                                                        9.540                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_135.clk (wendu/clk_us_syn_4)                 net                     3.095       3.095      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.095
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1002.979
 clock uncertainty                                                                  -0.000    1002.979
 clock recovergence pessimism                                                        0.287    1003.266
 Required time                                                                    1003.266            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.726ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     993.950 ns                                                      
 Start Point:             wendu/reg2_syn_142.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg2_syn_135.d[0] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.870ns  (logic 2.297ns, net 3.573ns, 39% logic)                
 Logic Levels:            6 ( LUT4=3 LUT2=2 LUT5=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_142.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_142.q[0]                                     clk2q                   0.146 r     3.592
 wendu/cur_state[0]_syn_3786.a[1] (wendu/cnt_us[4])          net  (fanout = 9)       0.571 r     4.163      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3786.f[1]                            cell (LUT4)             0.424 r     4.587
 wendu/cur_state[0]_syn_3756.a[0] (wendu/cur_state[0]_syn_3523) net  (fanout = 1)       0.594 r     5.181      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3756.f[0]                            cell (LUT5)             0.424 r     5.605
 wendu/cur_state[0]_syn_3792.a[1] (wendu/cur_state[0]_syn_3529) net  (fanout = 1)       0.456 r     6.061      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3792.f[1]                            cell (LUT4)             0.408 r     6.469
 wendu/cur_state[0]_syn_3812.d[1] (wendu/cur_state[0]_syn_3531) net  (fanout = 2)       0.738 r     7.207      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3812.f[1]                            cell (LUT2)             0.262 r     7.469
 wendu/cur_state[0]_syn_3789.d[1] (wendu/cur_state[2]_syn_1562) net  (fanout = 4)       0.601 r     8.070      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3789.f[1]                            cell (LUT4)             0.262 r     8.332
 wendu/reg2_syn_135.d[0] (wendu/cur_state[0]_syn_3534)       net  (fanout = 7)       0.613 r     8.945      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_135                                          path2reg0 (LUT2)        0.371       9.316
 Arrival time                                                                        9.316                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_135.clk (wendu/clk_us_syn_4)                 net                     3.095       3.095      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.095
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1002.979
 clock uncertainty                                                                  -0.000    1002.979
 clock recovergence pessimism                                                        0.287    1003.266
 Required time                                                                    1003.266            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             993.950ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     994.198 ns                                                      
 Start Point:             wendu/reg2_syn_148.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg2_syn_135.d[0] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Slow                                                            
 Data Path Delay:         5.650ns  (logic 2.304ns, net 3.346ns, 40% logic)                
 Logic Levels:            6 ( LUT4=3 LUT2=2 LUT5=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_148.clk (wendu/clk_us_syn_4)                 net                     3.446       3.446      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       3.446
---------------------------------------------------------------------------------------------------------
 wendu/reg2_syn_148.q[1]                                     clk2q                   0.146 r     3.592
 wendu/cur_state[0]_syn_3786.b[1] (wendu/cnt_us[7])          net  (fanout = 9)       0.344 r     3.936      ../../Src_al/DS18B20.v(51)
 wendu/cur_state[0]_syn_3786.f[1]                            cell (LUT4)             0.431 r     4.367
 wendu/cur_state[0]_syn_3756.a[0] (wendu/cur_state[0]_syn_3523) net  (fanout = 1)       0.594 r     4.961      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3756.f[0]                            cell (LUT5)             0.424 r     5.385
 wendu/cur_state[0]_syn_3792.a[1] (wendu/cur_state[0]_syn_3529) net  (fanout = 1)       0.456 r     5.841      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3792.f[1]                            cell (LUT4)             0.408 r     6.249
 wendu/cur_state[0]_syn_3812.d[1] (wendu/cur_state[0]_syn_3531) net  (fanout = 2)       0.738 r     6.987      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3812.f[1]                            cell (LUT2)             0.262 r     7.249
 wendu/cur_state[0]_syn_3789.d[1] (wendu/cur_state[2]_syn_1562) net  (fanout = 4)       0.601 r     7.850      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3789.f[1]                            cell (LUT4)             0.262 r     8.112
 wendu/reg2_syn_135.d[0] (wendu/cur_state[0]_syn_3534)       net  (fanout = 7)       0.613 r     8.725      ../../Src_al/DS18B20.v(44)
 wendu/reg2_syn_135                                          path2reg0 (LUT2)        0.371       9.096
 Arrival time                                                                        9.096                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg2_syn_135.clk (wendu/clk_us_syn_4)                 net                     3.095       3.095      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                               1000.000    1003.095
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116    1002.979
 clock uncertainty                                                                  -0.000    1002.979
 clock recovergence pessimism                                                        0.315    1003.294
 Required time                                                                    1003.294            
---------------------------------------------------------------------------------------------------------
 Slack                                                                             994.198ns          

---------------------------------------------------------------------------------------------------------

Hold checks:
---------------------------------------------------------------------------------------------------------
Paths for end point wendu/reg5_syn_118 (49 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.434 ns                                                        
 Start Point:             wendu/reg5_syn_118.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_118.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.495ns  (logic 0.378ns, net 0.117ns, 76% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_118.clk (wendu/clk_us_syn_4)                 net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_118.q[0]                                     clk2q                   0.109 r     2.878
 wendu/reg5_syn_118.d[1] (wendu/data_temp[6])                net  (fanout = 3)       0.117 r     2.995      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_118                                          path2reg1 (LUT4)        0.269       3.264
 Arrival time                                                                        3.264                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_118.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.273       2.830
 Required time                                                                       2.830            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.434ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.699 ns                                                        
 Start Point:             wendu/reg5_syn_118.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_118.c[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.760ns  (logic 0.430ns, net 0.330ns, 56% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_118.clk (wendu/clk_us_syn_4)                 net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_118.q[1]                                     clk2q                   0.109 r     2.878
 wendu/reg5_syn_118.c[1] (wendu/data_temp[5])                net  (fanout = 3)       0.330 r     3.208      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_118                                          path2reg1 (LUT4)        0.321       3.529
 Arrival time                                                                        3.529                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_118.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.273       2.830
 Required time                                                                       2.830            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.699ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      1.276 ns                                                        
 Start Point:             wendu/cur_state[0]_syn_3775.clk (rising edge triggered by clock clk_us)
 End Point:               wendu/reg5_syn_118.a[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         1.382ns  (logic 0.746ns, net 0.636ns, 53% logic)                
 Logic Levels:            2 ( LUT4=1 LUT3=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3775.clk (wendu/clk_us_syn_4)        net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3775.q[0]                            clk2q                   0.109 r     2.878
 wendu/cur_state[0]_syn_3797.c[1] (wendu/cur_state[5])       net  (fanout = 8)       0.256 r     3.134      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3797.f[1]                            cell (LUT3)             0.237 r     3.371
 wendu/reg5_syn_118.a[1] (wendu/cur_state[0]_syn_3556)       net  (fanout = 16)      0.380 r     3.751      ../../Src_al/DS18B20.v(44)
 wendu/reg5_syn_118                                          path2reg1 (LUT4)        0.400       4.151
 Arrival time                                                                        4.151                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_118.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.228       2.875
 Required time                                                                       2.875            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               1.276ns          

---------------------------------------------------------------------------------------------------------

Paths for end point wendu/reg5_syn_100 (49 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.443 ns                                                        
 Start Point:             wendu/reg5_syn_121.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_100.d[0] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.549ns  (logic 0.325ns, net 0.224ns, 59% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_121.clk (wendu/clk_us_syn_4)                 net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_121.q[0]                                     clk2q                   0.109 r     2.878
 wendu/reg5_syn_100.d[0] (wendu/data_temp[15])               net  (fanout = 3)       0.224 r     3.102      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_100                                          path2reg0 (LUT4)        0.216       3.318
 Arrival time                                                                        3.318                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_100.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.228       2.875
 Required time                                                                       2.875            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.443ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.814 ns                                                        
 Start Point:             wendu/reg5_syn_100.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_100.c[0] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.875ns  (logic 0.350ns, net 0.525ns, 40% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_100.clk (wendu/clk_us_syn_4)                 net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_100.q[0]                                     clk2q                   0.109 r     2.878
 wendu/reg5_syn_100.c[0] (wendu/data_temp[14])               net  (fanout = 3)       0.525 r     3.403      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_100                                          path2reg0 (LUT4)        0.241       3.644
 Arrival time                                                                        3.644                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_100.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.273       2.830
 Required time                                                                       2.830            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.814ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      1.216 ns                                                        
 Start Point:             wendu/cur_state[0]_syn_3775.clk (rising edge triggered by clock clk_us)
 End Point:               wendu/reg5_syn_100.b[0] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         1.322ns  (logic 0.579ns, net 0.743ns, 43% logic)                
 Logic Levels:            2 ( LUT4=1 LUT2=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3775.clk (wendu/clk_us_syn_4)        net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3775.q[0]                            clk2q                   0.109 r     2.878
 wendu/cur_state[0]_syn_3807.c[0] (wendu/cur_state[5])       net  (fanout = 8)       0.346 r     3.224      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3807.f[0]                            cell (LUT2)             0.151 r     3.375
 wendu/reg5_syn_100.b[0] (wendu/cur_state[0]_syn_3558)       net  (fanout = 16)      0.397 r     3.772      ../../Src_al/DS18B20.v(44)
 wendu/reg5_syn_100                                          path2reg0 (LUT4)        0.319       4.091
 Arrival time                                                                        4.091                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_100.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.228       2.875
 Required time                                                                       2.875            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               1.216ns          

---------------------------------------------------------------------------------------------------------

Paths for end point wendu/reg5_syn_121 (49 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.451 ns                                                        
 Start Point:             wendu/reg5_syn_109.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_121.d[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.557ns  (logic 0.325ns, net 0.232ns, 58% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_109.clk (wendu/clk_us_syn_4)                 net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_109.q[1]                                     clk2q                   0.109 r     2.878
 wendu/reg5_syn_121.d[1] (wendu/data_temp[1])                net  (fanout = 3)       0.232 r     3.110      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_121                                          path2reg1 (LUT4)        0.216       3.326
 Arrival time                                                                        3.326                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_121.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.228       2.875
 Required time                                                                       2.875            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.451ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      0.611 ns                                                        
 Start Point:             wendu/reg5_syn_121.clk (rising edge triggered by clock clk_us)  
 End Point:               wendu/reg5_syn_121.c[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.672ns  (logic 0.350ns, net 0.322ns, 52% logic)                
 Logic Levels:            1 ( LUT4=1 )                                                    

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_121.clk (wendu/clk_us_syn_4)                 net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/reg5_syn_121.q[1]                                     clk2q                   0.109 r     2.878
 wendu/reg5_syn_121.c[1] (wendu/data_temp[0])                net  (fanout = 2)       0.322 r     3.200      ../../Src_al/DS18B20.v(53)
 wendu/reg5_syn_121                                          path2reg1 (LUT4)        0.241       3.441
 Arrival time                                                                        3.441                  (1 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_121.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.273       2.830
 Required time                                                                       2.830            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.611ns          

---------------------------------------------------------------------------------------------------------

 Slack (hold check):      1.225 ns                                                        
 Start Point:             wendu/cur_state[0]_syn_3775.clk (rising edge triggered by clock clk_us)
 End Point:               wendu/reg5_syn_121.b[1] (rising edge triggered by clock clk_us) 
 Clock group:             clk_us                                                          
 Process:                 Fast                                                            
 Data Path Delay:         1.331ns  (logic 0.579ns, net 0.752ns, 43% logic)                
 Logic Levels:            2 ( LUT4=1 LUT2=1 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/cur_state[0]_syn_3775.clk (wendu/clk_us_syn_4)        net                     2.769       2.769      ../../Src_al/DS18B20.v(50)
 launch clock edge                                                                   0.000       2.769
---------------------------------------------------------------------------------------------------------
 wendu/cur_state[0]_syn_3775.q[0]                            clk2q                   0.109 r     2.878
 wendu/cur_state[0]_syn_3807.c[0] (wendu/cur_state[5])       net  (fanout = 8)       0.346 r     3.224      ../../Src_al/DS18B20.v(44)
 wendu/cur_state[0]_syn_3807.f[0]                            cell (LUT2)             0.151 r     3.375
 wendu/reg5_syn_121.b[1] (wendu/cur_state[0]_syn_3558)       net  (fanout = 16)      0.406 r     3.781      ../../Src_al/DS18B20.v(44)
 wendu/reg5_syn_121                                          path2reg1 (LUT4)        0.319       4.100
 Arrival time                                                                        4.100                  (2 lvl)       

 source latency                                                                      0.000       0.000                    
 wendu/clk_us_reg_syn_12.q[1]                                                        0.000       0.000                    
 wendu/clk_us_syn_3.clki (wendu/clk_us)                      net                     0.000       0.000      ../../Src_al/DS18B20.v(50)
 wendu/clk_us_syn_3.clko                                     cell (GCLK)             0.000       0.000                    
 wendu/reg5_syn_121.clk (wendu/clk_us_syn_4)                 net                     3.042       3.042      ../../Src_al/DS18B20.v(50)
 capture clock edge                                                                  0.000       3.042
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.061       3.103
 clock uncertainty                                                                   0.000       3.103
 clock recovergence pessimism                                                       -0.228       2.875
 Required time                                                                       2.875            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               1.225ns          

---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        Input delay                                                     
Set input delay: 14.5ns max, and 14.5ns min. 

24 endpoints analyzed totally, and 24 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Slowest input path delay 1.224ns
---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[0]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     3.080 ns                                                        
 Start Point:             AD_DATA[0] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[0]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         1.224ns  (logic 1.224ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[0]                                                  input                  14.500      14.500
 AD_DATA[0]_syn_4.ipad (AD_DATA[0])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(25)
 AD_DATA[0]_syn_4                                            path2reg                1.224      15.724
 Arrival time                                                                       15.724                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[0]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     1.978       1.978      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.644
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.160      18.804
 clock uncertainty                                                                  -0.000      18.804
 clock recovergence pessimism                                                        0.000      18.804
 Required time                                                                      18.804            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.080ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[1]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     3.080 ns                                                        
 Start Point:             AD_DATA[1] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[1]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         1.224ns  (logic 1.224ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[1]                                                  input                  14.500      14.500
 AD_DATA[1]_syn_4.ipad (AD_DATA[1])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(25)
 AD_DATA[1]_syn_4                                            path2reg                1.224      15.724
 Arrival time                                                                       15.724                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[1]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     1.978       1.978      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.644
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.160      18.804
 clock uncertainty                                                                  -0.000      18.804
 clock recovergence pessimism                                                        0.000      18.804
 Required time                                                                      18.804            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.080ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[2]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     3.080 ns                                                        
 Start Point:             AD_DATA[2] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[2]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         1.224ns  (logic 1.224ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[2]                                                  input                  14.500      14.500
 AD_DATA[2]_syn_4.ipad (AD_DATA[2])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(25)
 AD_DATA[2]_syn_4                                            path2reg                1.224      15.724
 Arrival time                                                                       15.724                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[2]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     1.978       1.978      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                 16.666      18.644
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.160      18.804
 clock uncertainty                                                                  -0.000      18.804
 clock recovergence pessimism                                                        0.000      18.804
 Required time                                                                      18.804            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.080ns          

---------------------------------------------------------------------------------------------------------

Fastest path checks:
---------------------------------------------------------------------------------------------------------
Paths for end point AD_DATA[0]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      13.126 ns                                                       
 Start Point:             AD_DATA[0] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[0]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         0.852ns  (logic 0.852ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[0]                                                  input                  14.500      14.500
 AD_DATA[0]_syn_4.ipad (AD_DATA[0])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(25)
 AD_DATA[0]_syn_4                                            path2reg                0.852      15.352
 Arrival time                                                                       15.352                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[0]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     2.065       2.065      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.065
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.161       2.226
 clock uncertainty                                                                   0.000       2.226
 clock recovergence pessimism                                                        0.000       2.226
 Required time                                                                       2.226            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              13.126ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[1]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      13.126 ns                                                       
 Start Point:             AD_DATA[1] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[1]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         0.852ns  (logic 0.852ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[1]                                                  input                  14.500      14.500
 AD_DATA[1]_syn_4.ipad (AD_DATA[1])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(25)
 AD_DATA[1]_syn_4                                            path2reg                0.852      15.352
 Arrival time                                                                       15.352                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[1]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     2.065       2.065      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.065
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.161       2.226
 clock uncertainty                                                                   0.000       2.226
 clock recovergence pessimism                                                        0.000       2.226
 Required time                                                                       2.226            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              13.126ns          

---------------------------------------------------------------------------------------------------------

Paths for end point AD_DATA[2]_syn_4 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      13.126 ns                                                       
 Start Point:             AD_DATA[2] (rising edge triggered by clock clk_vir)             
 End Point:               AD_DATA[2]_syn_4.ipad (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Input Delay:             14.500 ns                                                       
 Data Path Delay:         0.852ns  (logic 0.852ns, net 0.000ns, 100% logic)               
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 launch clock latency                                        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 AD_DATA[2]                                                  input                  14.500      14.500
 AD_DATA[2]_syn_4.ipad (AD_DATA[2])                          net  (fanout = 1)       0.000 f    14.500      ../../Src_al/IFOG501_2B.v(25)
 AD_DATA[2]_syn_4                                            path2reg                0.852      15.352
 Arrival time                                                                       15.352                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 AD_DATA[2]_syn_4.ipclk (signal_process/demodu/clk_in)       net                     2.065       2.065      ../../Src_al/Demodulation.v(51)
 capture clock edge                                                                  0.000       2.065
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.161       2.226
 clock uncertainty                                                                   0.000       2.226
 clock recovergence pessimism                                                        0.000       2.226
 Required time                                                                       2.226            
---------------------------------------------------------------------------------------------------------
 Slack                                                                              13.126ns          

---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        Path Delay                                                      
Path delay: 8.032ns max

7 endpoints analyzed totally, and 7 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Slowest path delay 0.815ns
---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.101 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_42.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.815ns  (logic 0.289ns, net 0.526ns, 35% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_42.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_42.q[1] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33.mi[1] (signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[4]) net  (fanout = 1)       0.526 r     0.672      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33  path2reg1               0.143       0.815
 Arrival time                                                                        0.815                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.101ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.148 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_24.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.768ns  (logic 0.289ns, net 0.479ns, 37% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_24.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_24.q[0] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28.mi[0] (signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[6]) net  (fanout = 1)       0.479 r     0.625      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28  path2reg0               0.143       0.768
 Arrival time                                                                        0.768                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.148ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.151 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.765ns  (logic 0.289ns, net 0.476ns, 37% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.q[0] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29.mi[1] (signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[1]) net  (fanout = 1)       0.476 r     0.622      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29  path2reg1               0.143       0.765
 Arrival time                                                                        0.765                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.151ns          

---------------------------------------------------------------------------------------------------------

Fastest path checks:
---------------------------------------------------------------------------------------------------------

=========================================================================================================
Timing constraint:        Path Delay                                                      
Path delay: 8.032ns max

7 endpoints analyzed totally, and 7 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Slowest path delay 0.891ns
---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.025 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.891ns  (logic 0.289ns, net 0.602ns, 32% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.q[1] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[0] (signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[2]) net  (fanout = 1)       0.602 r     0.748      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31  path2reg0               0.143       0.891
 Arrival time                                                                        0.891                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.025ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.145 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.771ns  (logic 0.289ns, net 0.482ns, 37% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.q[0] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.mi[0] (signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[5]) net  (fanout = 1)       0.482 r     0.628      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28  path2reg0               0.143       0.771
 Arrival time                                                                        0.771                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.145ns          

---------------------------------------------------------------------------------------------------------

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (max path):        7.145 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.771ns  (logic 0.289ns, net 0.482ns, 37% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[3]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk (signal_process/demodu/clk_in) net                     0.000       0.000      ../../Src_al/Demodulation.v(51)
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.q[1] clk2q                   0.146 r     0.146
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31.mi[0] (signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[0]) net  (fanout = 1)       0.482 r     0.628      ../../al_ip/Asys_fifo56X16.v(303)
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31  path2reg0               0.143       0.771
 Arrival time                                                                        0.771                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31.clk (signal_process/clk) net                     0.000       0.000      ../../Src_al/SignalProcessing.v(61)
---------------------------------------------------------------------------------------------------------
 delay budget                                                                        8.032       8.032
 cell setup                                                                         -0.116       7.916
 clock uncertainty                                                                  -0.000       7.916
 clock recovergence pessimism                                                        0.000       7.916
 Required time                                                                       7.916            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               7.145ns          

---------------------------------------------------------------------------------------------------------

Fastest path checks:
---------------------------------------------------------------------------------------------------------

=========================================================================================================
Timing summary:                                                                           
---------------------------------------------------------------------------------------------------------
Constraint path number: 49036 (STA coverage = 95.66%)
Timing violations: 0 setup errors, and 0 hold errors.
Minimal setup slack: 0.158, minimal hold slack: 0.089

Timing group statistics: 
	Clock constraints: 
	  Clock Name                                  Min Period     Max Freq           Skew      Fanout            TNS
	  CLK120/pll_inst.clkc[0] (120.0MHz)             8.175ns     122.324MHz        0.480ns       465        0.000ns
	  CLK120/pll_inst.clkc[3] (60.0MHz)             13.586ns      73.605MHz        0.326ns       109        0.000ns
	  clk_us (1000.0KHz)                             7.625ns     131.148MHz        0.254ns        40        0.000ns
	Minimum input arrival time before clock: 1.224ns
	Maximum output required time after clock: no constraint path
	Maximum combinational path delay: no constraint path


Warning: No clock constraint on 1 clock net(s): 
	dlycnt_n1_syn_2

	Exceptions:

		Check Type:	MIN
		----------------------------------------------------------------------------------------------------
		       Path Num     Constraint                                                                      
		              7     set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
		              7     set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]

---------------------------------------------------------------------------------------------------------
