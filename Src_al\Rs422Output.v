`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	Integration
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	
// Revision 1.01 - File Created
// Additional Comments: 
//Time sequence diagram
//   ___
//  |   |
//__|   |_________________________________________//clk_DA
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate
//0………………………………………………………………………………………………………  242 245
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////

module Rs422Output
#(
	parameter bPOLAR=1,
	parameter iWID_IN=56,
	parameter iWID_OUT=32,
	parameter iDELAYED=150,
	parameter iOUTPUT_SCALE=1350
)
(
	input						rst_n,
	input						clk, //120MHz reference clock
	input						output_drive,
	input						RxTransmit, //RxTransmit signal,100HZ同步信号
	input						polarity,
	input		[iWID_IN-1:0]	din, 
	output reg					transmit,  //RxTransmit clock for picoblaze
	output reg 	[iWID_OUT-1:0]	dout  	  //data out
);	
////////////////////////////////////////////////////////////////////////////////
reg [1:0] 			RxTr_dy;	//RxTransmit Signal delay register
reg	[1:0]			output_dy;  //output_drive Signal delay register
reg [7:0]			count_pos;//counter for positive preparing data
reg [79:0]			p_sum;//sum of velocity
reg [iWID_IN-1:0]	sum; //sum of velocity
reg [iWID_IN-1:0]	din_temp;//sum of velocity
reg [iWID_OUT-1:0] 	RS_dout;
reg	[iWID_IN-1:0]	sum_dy;
reg	[79:0]			p_sum_dy;
////////////////////////////////////////////////////////////////////////////////
reg	[3:0]	next_state;
reg	[3:0]	trans_state;
reg	[3:0]	dalay_cout;
localparam	idle_s 				= 4'd0 ;
localparam	wait_1us_s 			= 4'd1 ;
localparam	dalay_state 		= 4'd2 ;
localparam	check_data_stable_s = 4'd3 ;
localparam	transmit_data_s 	= 4'd4 ;
localparam	clear_data_s 		= 4'd5 ;

//RxTransmit:Delay two beats to generate rising edge signal
always@(posedge clk)begin
	RxTr_dy <= {RxTr_dy[0],RxTransmit};
end

//output_drive:Delay two beats to generate rising edge signal
always@(posedge clk)begin
	output_dy <= {output_dy[0],output_drive};
end

//Three state machine
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		trans_state <= idle_s;
	end
	else begin
		trans_state <= next_state;
	end
end

always@(*)begin
	case(trans_state)
		idle_s:	begin
					if(RxTr_dy[1:0] == 2'b01)begin//RxTransmit Rising edge
						next_state <= wait_1us_s;
					end
					else begin
						next_state <= idle_s;
					end
				end 
		wait_1us_s:begin
					if(count_pos == iDELAYED -5)
						next_state <= dalay_state;
					else
						next_state <= wait_1us_s;
				end
		dalay_state:begin
					if(dalay_cout == 4'd12)
						next_state <= check_data_stable_s;
					else
						next_state <= dalay_state;
				end
		check_data_stable_s:begin
					if((output_dy == 2'b01) && (polarity == 1'b1))
						next_state <= transmit_data_s;//transmit_data_s
					else
						next_state <= check_data_stable_s;//check_data_stable_s
				end
		transmit_data_s:next_state <= clear_data_s;
		clear_data_s:	next_state <= idle_s;
		default:		next_state <= idle_s;
	endcase
end

//Valid status of data:transmit
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		transmit <= 1'b0;
	end
	else if((trans_state == transmit_data_s)||(trans_state == clear_data_s))begin
		transmit <= 1'b1;
	end
	else begin
		transmit <= 1'b0;
	end
end

//Count 1 millisecond
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		count_pos <= 8'd0;
	end
	else if(trans_state == wait_1us_s)begin
		count_pos <= count_pos + 1'b1;
	end
	else begin
		count_pos <= 8'd0;
	end
end

//delay 4 clock(120MHz)
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		dalay_cout <= 4'd0;
	end
	else if(trans_state == dalay_state)begin
		dalay_cout <= dalay_cout + 1'b1;
	end
	else begin
		dalay_cout <= 4'd0;
	end
end

//Cache input data
always@(posedge clk)begin
	if(polarity)
		din_temp <= din;
	else
		din_temp <= din_temp;
end

//Input angular velocity integral
always@(posedge clk)begin
	if(trans_state == clear_data_s)
		sum <= 56'd0;
	else if((trans_state == idle_s)||(trans_state == wait_1us_s))
		sum <= sum+din_temp;
	else
		sum <= sum;
end

//sum:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	sum_dy <= (dalay_cout == 4'd3)?sum : sum_dy;
end

always@(posedge clk or negedge rst_n)begin
	if(~rst_n)
		p_sum <= 80'd0;
	else if(dalay_cout == 4'd8)
		p_sum <= sum*iOUTPUT_SCALE;
	else
		p_sum <= p_sum;
end

//p_sum:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	p_sum_dy <= p_sum;
end

//out data
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		RS_dout <=32'd0;
	end
	else if(trans_state == transmit_data_s)begin
		RS_dout <= p_sum_dy[iWID_OUT+23:24];
	end
	else begin
		RS_dout <= RS_dout;
	end
end

//RS_dout:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	if(bPOLAR)
		dout <= ~RS_dout + 1'b1;
	else 
		dout <= RS_dout;
end

endmodule