`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	8K21
// Module Name:    	speed_select_Tx
// Project Name: 	8K21
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	 
// Revision 1.01 - File Created
// Additional Comments: 
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module speed_select_Tx
#(
    parameter SYS_FREQ   = *********,  //输入时钟
	parameter UART_BAUD  = 115200     //波特率
)
(
	input       clk      ,	//24MHz主时钟
	input       rst_n    ,	//低电平复位有效
	input       bps_start,	//开始接收或者发送数据，波特率时钟启动信号置位
	output reg  clk_bps  	//clk_bps的高电平为接收或者发送数据位的中间采样点 
);

//以下波特率分频计数值可参照上面的参数进行更改
localparam BPS_PARA	= SYS_FREQ/UART_BAUD; //波特率为115200时的分频计数值
localparam BPS_PARA_2 = BPS_PARA/2;       //波特率为115200时的分频计
reg     [12:0]  cnt       ; //分频计数
reg             clk_bps_r ; //波特率时蛹拇嫫?
reg     [2:0]   uart_ctrl ; //uart波特率选择寄存器
//----------------------------------------------------------

always @ (posedge clk or negedge rst_n)begin
	if(rst_n==1'b0) 
		cnt <= 13'd0;
	else if((cnt == BPS_PARA) || !bps_start) 
		cnt <= 13'd0;	//波特率计数清零
	else 
		cnt <= cnt+1'b1; //波特率时钟计数启动
end

always @ (posedge clk or negedge rst_n)begin
	if(rst_n==1'b0) 
		clk_bps_r <= 1'b0;
	else if(cnt == BPS_PARA_2) 
		clk_bps_r <= 1'b1;	// clk_bps_r高电平为接收数据位的中间采样点,同时也作为发送数据的数据改变点
	else 
		clk_bps_r <= 1'b0;
end

//clk_bps_r:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	clk_bps <= clk_bps_r;
end

endmodule