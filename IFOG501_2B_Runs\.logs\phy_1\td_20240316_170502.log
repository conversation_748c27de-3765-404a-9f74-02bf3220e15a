============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Sat Mar 16 17:05:02 2024

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 16 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1600 instances
RUN-0007 : 385 luts, 968 seqs, 125 mslices, 75 lslices, 32 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2132 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1611 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     233     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     287     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1598 instances, 385 luts, 968 seqs, 200 slices, 23 macros(200 instances: 125 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7608, tnet num: 2130, tinst num: 1598, tnode num: 10734, tedge num: 12819.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.202474s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (54.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 615254
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1598.
PHY-3001 : End clustering;  0.000007s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 522186, overlap = 18
PHY-3002 : Step(2): len = 430037, overlap = 11.25
PHY-3002 : Step(3): len = 341860, overlap = 15.75
PHY-3002 : Step(4): len = 330222, overlap = 13.5
PHY-3002 : Step(5): len = 317516, overlap = 20.25
PHY-3002 : Step(6): len = 307256, overlap = 20.25
PHY-3002 : Step(7): len = 302202, overlap = 20.25
PHY-3002 : Step(8): len = 294184, overlap = 20.25
PHY-3002 : Step(9): len = 287814, overlap = 20.25
PHY-3002 : Step(10): len = 280641, overlap = 20.25
PHY-3002 : Step(11): len = 274796, overlap = 20.25
PHY-3002 : Step(12): len = 266768, overlap = 20.25
PHY-3002 : Step(13): len = 262055, overlap = 20.25
PHY-3002 : Step(14): len = 256381, overlap = 20.25
PHY-3002 : Step(15): len = 250359, overlap = 20.25
PHY-3002 : Step(16): len = 244300, overlap = 20.25
PHY-3002 : Step(17): len = 240729, overlap = 20.25
PHY-3002 : Step(18): len = 233729, overlap = 20.25
PHY-3002 : Step(19): len = 228607, overlap = 20.25
PHY-3002 : Step(20): len = 224614, overlap = 20.25
PHY-3002 : Step(21): len = 221307, overlap = 20.25
PHY-3002 : Step(22): len = 208726, overlap = 20.25
PHY-3002 : Step(23): len = 204114, overlap = 20.25
PHY-3002 : Step(24): len = 201558, overlap = 20.25
PHY-3002 : Step(25): len = 196627, overlap = 20.25
PHY-3002 : Step(26): len = 178381, overlap = 20.25
PHY-3002 : Step(27): len = 175128, overlap = 20.25
PHY-3002 : Step(28): len = 172839, overlap = 20.25
PHY-3002 : Step(29): len = 145131, overlap = 20.25
PHY-3002 : Step(30): len = 132652, overlap = 20.25
PHY-3002 : Step(31): len = 131413, overlap = 20.25
PHY-3002 : Step(32): len = 127141, overlap = 20.25
PHY-3002 : Step(33): len = 124226, overlap = 20.25
PHY-3002 : Step(34): len = 119550, overlap = 20.25
PHY-3002 : Step(35): len = 117325, overlap = 20.25
PHY-3002 : Step(36): len = 114073, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.48604e-05
PHY-3002 : Step(37): len = 116058, overlap = 13.5
PHY-3002 : Step(38): len = 116625, overlap = 6.75
PHY-3002 : Step(39): len = 114426, overlap = 9
PHY-3002 : Step(40): len = 112627, overlap = 15.75
PHY-3002 : Step(41): len = 110478, overlap = 18
PHY-3002 : Step(42): len = 109635, overlap = 13.5
PHY-3002 : Step(43): len = 107867, overlap = 13.5
PHY-3002 : Step(44): len = 106905, overlap = 13.5
PHY-3002 : Step(45): len = 104836, overlap = 15.75
PHY-3002 : Step(46): len = 103206, overlap = 15.75
PHY-3002 : Step(47): len = 99338, overlap = 6.75
PHY-3002 : Step(48): len = 96112.9, overlap = 13.5
PHY-3002 : Step(49): len = 96141.9, overlap = 13.5
PHY-3002 : Step(50): len = 94488.5, overlap = 13.5
PHY-3002 : Step(51): len = 92378.2, overlap = 11.25
PHY-3002 : Step(52): len = 89598.7, overlap = 13.5
PHY-3002 : Step(53): len = 89114.2, overlap = 13.5
PHY-3002 : Step(54): len = 87786.8, overlap = 11.25
PHY-3002 : Step(55): len = 86091.4, overlap = 11.25
PHY-3002 : Step(56): len = 85999.2, overlap = 11.25
PHY-3002 : Step(57): len = 84583.5, overlap = 9
PHY-3002 : Step(58): len = 83600.9, overlap = 13.5
PHY-3002 : Step(59): len = 82976.7, overlap = 13.625
PHY-3002 : Step(60): len = 82145.1, overlap = 11.5
PHY-3002 : Step(61): len = 80515.1, overlap = 6.75
PHY-3002 : Step(62): len = 78483, overlap = 9.125
PHY-3002 : Step(63): len = 76957.1, overlap = 9.5
PHY-3002 : Step(64): len = 76217.9, overlap = 13.9375
PHY-3002 : Step(65): len = 75385.1, overlap = 14.25
PHY-3002 : Step(66): len = 74922.9, overlap = 12.0625
PHY-3002 : Step(67): len = 74569.1, overlap = 7.25
PHY-3002 : Step(68): len = 73273.7, overlap = 9.4375
PHY-3002 : Step(69): len = 71619.4, overlap = 9.5625
PHY-3002 : Step(70): len = 71194.3, overlap = 13.9375
PHY-3002 : Step(71): len = 70740, overlap = 11.6875
PHY-3002 : Step(72): len = 70003.9, overlap = 9.4375
PHY-3002 : Step(73): len = 69878.1, overlap = 11.6875
PHY-3002 : Step(74): len = 69617.8, overlap = 13.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000189721
PHY-3002 : Step(75): len = 69496.4, overlap = 11.75
PHY-3002 : Step(76): len = 69458.6, overlap = 9.25
PHY-3002 : Step(77): len = 69509, overlap = 11.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000379441
PHY-3002 : Step(78): len = 69597.9, overlap = 7
PHY-3002 : Step(79): len = 69721.8, overlap = 4.8125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005623s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039801s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (39.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(80): len = 70551.1, overlap = 6.09375
PHY-3002 : Step(81): len = 69319.5, overlap = 6.09375
PHY-3002 : Step(82): len = 67634.8, overlap = 6.375
PHY-3002 : Step(83): len = 65616.9, overlap = 6.125
PHY-3002 : Step(84): len = 63369.8, overlap = 7.25
PHY-3002 : Step(85): len = 62288.2, overlap = 7.59375
PHY-3002 : Step(86): len = 59746.3, overlap = 9.46875
PHY-3002 : Step(87): len = 57826.9, overlap = 9.71875
PHY-3002 : Step(88): len = 56254.4, overlap = 9.9375
PHY-3002 : Step(89): len = 55028.1, overlap = 10.625
PHY-3002 : Step(90): len = 54025.5, overlap = 13.9062
PHY-3002 : Step(91): len = 52545.2, overlap = 15.4375
PHY-3002 : Step(92): len = 50992.5, overlap = 9.46875
PHY-3002 : Step(93): len = 49383.7, overlap = 8.90625
PHY-3002 : Step(94): len = 48371.9, overlap = 9.71875
PHY-3002 : Step(95): len = 47942.4, overlap = 9.8125
PHY-3002 : Step(96): len = 47392, overlap = 10.5
PHY-3002 : Step(97): len = 47287.3, overlap = 8.5
PHY-3002 : Step(98): len = 46796.5, overlap = 9.46875
PHY-3002 : Step(99): len = 46446.4, overlap = 9.875
PHY-3002 : Step(100): len = 46296.5, overlap = 9.84375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000306602
PHY-3002 : Step(101): len = 46057.7, overlap = 9.84375
PHY-3002 : Step(102): len = 45992.7, overlap = 9.84375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000613205
PHY-3002 : Step(103): len = 46356.9, overlap = 9.78125
PHY-3002 : Step(104): len = 46409.5, overlap = 9.53125
PHY-3002 : Step(105): len = 46611.8, overlap = 9.34375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046657s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (67.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000131755
PHY-3002 : Step(106): len = 46640.1, overlap = 59.8438
PHY-3002 : Step(107): len = 46642.1, overlap = 61.5
PHY-3002 : Step(108): len = 47859.2, overlap = 55.6875
PHY-3002 : Step(109): len = 49011.4, overlap = 48.2188
PHY-3002 : Step(110): len = 49383.1, overlap = 47.2188
PHY-3002 : Step(111): len = 49081.5, overlap = 43.125
PHY-3002 : Step(112): len = 48473.7, overlap = 40.9062
PHY-3002 : Step(113): len = 47924, overlap = 41.5625
PHY-3002 : Step(114): len = 47905.3, overlap = 40.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000263511
PHY-3002 : Step(115): len = 48216, overlap = 40.2812
PHY-3002 : Step(116): len = 48662.6, overlap = 40.4062
PHY-3002 : Step(117): len = 48618.6, overlap = 39.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000527021
PHY-3002 : Step(118): len = 49236.9, overlap = 39.75
PHY-3002 : Step(119): len = 49784, overlap = 40.7812
PHY-3002 : Step(120): len = 50063.2, overlap = 38.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7608, tnet num: 2130, tinst num: 1598, tnode num: 10734, tedge num: 12819.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 78.88 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2132.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 52712, over cnt = 236(0%), over = 1052, worst = 20
PHY-1001 : End global iterations;  0.060438s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (51.7%)

PHY-1001 : Congestion index: top1 = 45.91, top5 = 25.54, top10 = 15.90, top15 = 11.25.
PHY-1001 : End incremental global routing;  0.098570s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (47.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046497s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (67.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.168287s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (46.4%)

OPT-1001 : Current memory(MB): used = 208, reserve = 181, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1616/2132.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 52712, over cnt = 236(0%), over = 1052, worst = 20
PHY-1002 : len = 59680, over cnt = 149(0%), over = 336, worst = 15
PHY-1002 : len = 63328, over cnt = 77(0%), over = 119, worst = 8
PHY-1002 : len = 64352, over cnt = 28(0%), over = 33, worst = 4
PHY-1002 : len = 65016, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.067763s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (69.2%)

PHY-1001 : Congestion index: top1 = 39.25, top5 = 25.51, top10 = 18.11, top15 = 13.22.
OPT-1001 : End congestion update;  0.099907s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (62.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2130 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039811s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Start: WNS 873 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 873 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.141565s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (44.1%)

OPT-1001 : Current memory(MB): used = 211, reserve = 184, peak = 211.
OPT-1001 : End physical optimization;  0.489722s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (38.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 385 LUT to BLE ...
SYN-4008 : Packed 385 LUT and 183 SEQ to BLE.
SYN-4003 : Packing 785 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 121 single LUT's are left
SYN-4006 : 682 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1067/1372 primitive instances ...
PHY-3001 : End packing;  0.037680s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (82.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 816 instances
RUN-1001 : 385 mslices, 384 lslices, 32 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1963 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1444 nets have 2 pins
RUN-1001 : 397 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 814 instances, 769 slices, 23 macros(200 instances: 125 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49928.2, Over = 63.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6412, tnet num: 1961, tinst num: 814, tnode num: 8662, tedge num: 11231.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.210624s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (37.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.99666e-05
PHY-3002 : Step(121): len = 49403.4, overlap = 65.25
PHY-3002 : Step(122): len = 48982.5, overlap = 62.75
PHY-3002 : Step(123): len = 48854.6, overlap = 63
PHY-3002 : Step(124): len = 48411.4, overlap = 65.25
PHY-3002 : Step(125): len = 48093.7, overlap = 63.5
PHY-3002 : Step(126): len = 47942.7, overlap = 64.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.99331e-05
PHY-3002 : Step(127): len = 48207.4, overlap = 62
PHY-3002 : Step(128): len = 48905.4, overlap = 61.5
PHY-3002 : Step(129): len = 49836.8, overlap = 60
PHY-3002 : Step(130): len = 50296.2, overlap = 56.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000119866
PHY-3002 : Step(131): len = 50757.6, overlap = 53
PHY-3002 : Step(132): len = 51452.3, overlap = 50.25
PHY-3002 : Step(133): len = 51607.4, overlap = 49.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.081343s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (19.2%)

PHY-3001 : Trial Legalized: Len = 63268.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.034983s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000569262
PHY-3002 : Step(134): len = 60999.7, overlap = 4.25
PHY-3002 : Step(135): len = 59179.2, overlap = 9.75
PHY-3002 : Step(136): len = 57438.9, overlap = 17.5
PHY-3002 : Step(137): len = 56547.6, overlap = 17.75
PHY-3002 : Step(138): len = 55917.7, overlap = 19.25
PHY-3002 : Step(139): len = 55516, overlap = 23.5
PHY-3002 : Step(140): len = 55191.8, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00113852
PHY-3002 : Step(141): len = 55532.8, overlap = 24
PHY-3002 : Step(142): len = 55678.5, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00227705
PHY-3002 : Step(143): len = 55809.1, overlap = 23
PHY-3002 : Step(144): len = 55958.2, overlap = 23.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003986s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 60163.6, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003874s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 19 instances has been re-located, deltaX = 5, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 60299.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6412, tnet num: 1961, tinst num: 814, tnode num: 8662, tedge num: 11231.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 133/1963.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 68256, over cnt = 156(0%), over = 232, worst = 7
PHY-1002 : len = 68928, over cnt = 80(0%), over = 102, worst = 4
PHY-1002 : len = 70128, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70160, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.102816s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (15.2%)

PHY-1001 : Congestion index: top1 = 33.21, top5 = 23.77, top10 = 18.32, top15 = 14.17.
PHY-1001 : End incremental global routing;  0.140433s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (22.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.041405s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (37.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.202238s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (23.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 187, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1738/1963.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 70208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003313s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (471.6%)

PHY-1001 : Congestion index: top1 = 33.21, top5 = 23.77, top10 = 18.32, top15 = 14.17.
OPT-1001 : End congestion update;  0.035942s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032834s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Start: WNS 873 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 873 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.069840s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (44.7%)

OPT-1001 : Current memory(MB): used = 215, reserve = 189, peak = 215.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039291s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1738/1963.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 70208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003268s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.21, top5 = 23.77, top10 = 18.32, top15 = 14.17.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034870s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 873 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 873ps with logic level 1 
RUN-1001 :       #2 path slack 873ps with logic level 1 
RUN-1001 :       #3 path slack 873ps with logic level 1 
RUN-1001 :       #4 path slack 873ps with logic level 1 
RUN-1001 :       #5 path slack 873ps with logic level 1 
RUN-1001 :       #6 path slack 873ps with logic level 1 
RUN-1001 :       #7 path slack 873ps with logic level 1 
RUN-1001 :       #8 path slack 873ps with logic level 1 
RUN-1001 :       #9 path slack 873ps with logic level 1 
RUN-1001 :       #10 path slack 873ps with logic level 1 
OPT-1001 : End physical optimization;  0.580222s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (24.2%)

RUN-1003 : finish command "place" in  3.772805s wall, 1.093750s user + 0.281250s system = 1.375000s CPU (36.4%)

RUN-1004 : used memory is 188 MB, reserved memory is 161 MB, peak memory is 216 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |    off     |       off        |        
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 16 thread(s)
RUN-1001 : There are total 816 instances
RUN-1001 : 385 mslices, 384 lslices, 32 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1963 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1444 nets have 2 pins
RUN-1001 : 397 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6412, tnet num: 1961, tinst num: 814, tnode num: 8662, tedge num: 11231.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 385 mslices, 384 lslices, 32 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 66752, over cnt = 158(0%), over = 238, worst = 7
PHY-1002 : len = 67752, over cnt = 84(0%), over = 106, worst = 4
PHY-1002 : len = 69024, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 69120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.101914s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.38, top10 = 18.06, top15 = 13.95.
PHY-1001 : End global routing;  0.139049s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (22.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 213, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 477, peak = 499.
PHY-1001 : End build detailed router design. 2.459060s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (40.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 37680, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.792497s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (39.4%)

PHY-1001 : Current memory(MB): used = 531, reserve = 510, peak = 531.
PHY-1001 : End phase 1; 0.796067s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (39.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 186304, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 512, peak = 533.
PHY-1001 : End initial routed; 0.509297s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (52.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1751(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.285   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.250049s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (37.5%)

PHY-1001 : Current memory(MB): used = 537, reserve = 515, peak = 537.
PHY-1001 : End phase 2; 0.759390s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (47.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186304, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.010597s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (147.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186312, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 1; 0.016059s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1751(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.285   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.248150s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (18.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.129113s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (60.5%)

PHY-1001 : Current memory(MB): used = 553, reserve = 532, peak = 553.
PHY-1001 : End phase 3; 0.494025s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (34.8%)

PHY-1003 : Routed, final wirelength = 186312
PHY-1001 : Current memory(MB): used = 553, reserve = 532, peak = 553.
PHY-1001 : End export database. 0.005315s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  4.647749s wall, 1.906250s user + 0.015625s system = 1.921875s CPU (41.4%)

RUN-1003 : finish command "route" in  5.040209s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (40.6%)

RUN-1004 : used memory is 481 MB, reserved memory is 463 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   15
  #output                  18
  #inout                    1

Utilization Statistics
#lut                      793   out of  19600    4.05%
#reg                     1019   out of  19600    5.20%
#le                      1475
  #lut only               456   out of   1475   30.92%
  #reg only               682   out of   1475   46.24%
  #lut&reg                337   out of   1475   22.85%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       32   out of     66   48.48%
  #ireg                    12
  #oreg                    16
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         444
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         112
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT        P23        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[10]      INPUT        P37        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[9]       INPUT         P8        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[8]       INPUT        P84        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[7]       INPUT        P68        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[6]       INPUT        P27        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[5]       INPUT        P41        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[4]       INPUT         P4        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[3]       INPUT        P72        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[2]       INPUT        P57        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[1]       INPUT        P38        LVCMOS25          N/A           N/A        IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
    clk_in         INPUT        P34        LVCMOS25          N/A           N/A        NONE    
  DA_DATA[13]     OUTPUT         P5        LVCMOS25           8            N/A        NONE    
  DA_DATA[12]     OUTPUT        P62        LVCMOS25           8            N/A        NONE    
  DA_DATA[11]     OUTPUT        P18        LVCMOS33           8            N/A        OREG    
  DA_DATA[10]     OUTPUT         P3        LVCMOS25           8            N/A        OREG    
  DA_DATA[9]      OUTPUT        P76        LVCMOS25           8            N/A        OREG    
  DA_DATA[8]      OUTPUT        P77        LVCMOS25           8            N/A        OREG    
  DA_DATA[7]      OUTPUT        P49        LVCMOS25           8            N/A        OREG    
  DA_DATA[6]      OUTPUT        P74        LVCMOS25           8            N/A        OREG    
  DA_DATA[5]      OUTPUT        P75        LVCMOS25           8            N/A        OREG    
  DA_DATA[4]      OUTPUT        P52        LVCMOS25           8            N/A        OREG    
  DA_DATA[3]      OUTPUT        P59        LVCMOS25           8            N/A        OREG    
  DA_DATA[2]      OUTPUT        P31        LVCMOS25           8            N/A        OREG    
  DA_DATA[1]      OUTPUT         P2        LVCMOS25           8            N/A        OREG    
  DA_DATA[0]      OUTPUT        P69        LVCMOS25           8            N/A        OREG    
      TXD         OUTPUT        P39        LVCMOS25           8            N/A        OREG    
  TxTransmit      OUTPUT        P81        LVCMOS25           8            N/A        OREG    
    clk_ADo       OUTPUT        P29        LVCMOS25           8            N/A       ODDRX1   
    clk_DA        OUTPUT        P86        LVCMOS25           8            N/A        OREG    
      dq           INOUT        P13        LVCMOS33           8            N/A        TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1475   |593     |200     |1048    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1065   |276     |107     |866     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |41     |35      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |521    |104     |53      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |14      |0       |30      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |137    |28      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |33     |5       |0       |33      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |90      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |122    |115     |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |21     |21      |0       |13      |0       |0       |
|    U2                      |Ctrl_Data                                        |66     |66      |0       |29      |0       |0       |
|  wendu                     |DS18B20                                          |205    |160     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1410  
    #2          2       262   
    #3          3       123   
    #4          4        12   
    #5        5-10       83   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.98            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6412, tnet num: 1961, tinst num: 814, tnode num: 8662, tedge num: 11231.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1961 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 16 threads.
BIT-1002 : Init instances completely, inst num: 814
BIT-1002 : Init pips with 16 threads.
BIT-1002 : Init pips completely, net num: 1963, pip num: 14615
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 16 threads.
BIT-1003 : Generate bitstream completely, there are 1322 valid insts, and 38387 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  1.605731s wall, 9.843750s user + 0.015625s system = 9.859375s CPU (614.0%)

RUN-1004 : used memory is 493 MB, reserved memory is 469 MB, peak memory is 693 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240316_170502.log"
