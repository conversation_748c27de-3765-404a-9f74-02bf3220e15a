`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	SquareWaveGenerator
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	
// Revision 1.01 - File Created
// Additional Comments: 
//////////////////////////////////////////////////////////////////////////////////
module SquareWaveGenerator
#(
	parameter 	iTRANSMIT_COFF=800000 //transmit signal divider coff,10ms default
)
(
	input		rst_n,
	input		clk, //80MHz reference clock
	output 	reg	clk_out	//clock output
);

reg [19:0] count;	
//counter
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)
		count<=0;
	else if(count==iTRANSMIT_COFF-1) 
		count<=0;
	else
		count<=count+1;
end
	
//iTRANSMIT_COFF divider
always@(posedge clk or negedge rst_n)begin
	if(~rst_n)
		clk_out<=0;
	else if(count==(iTRANSMIT_COFF/2-1))
		clk_out<=1'b1;
	else if(count==iTRANSMIT_COFF-1)
		clk_out<=1'b0;
	else
		clk_out<=clk_out;
end

endmodule