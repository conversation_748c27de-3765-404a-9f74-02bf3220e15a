============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Mon Mar 18 20:43:51 2024

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/IFOGSOFT/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/IFOGSOFT/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  3.174670s wall, 2.875000s user + 0.046875s system = 2.921875s CPU (92.0%)

RUN-1004 : used memory is 493 MB, reserved memory is 461 MB, peak memory is 521 MB
GUI-5004 WARNING: AD_DATA[11] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[10] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[9] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[8] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[7] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[6] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[5] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[4] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[3] has not been assigned a location ...
GUI-5004 WARNING: AD_DATA[2] has not been assigned a location ...
GUI-5004 Similar messages will be suppressed.
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_nets wendu/clk_us -hier"
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/IFOGSOFT/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/IFOGSOFT/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.008092s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (44.9%)

RUN-1004 : used memory is 567 MB, reserved memory is 519 MB, peak memory is 594 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/IFOGSOFT/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
