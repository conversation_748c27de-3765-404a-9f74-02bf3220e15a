============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Wed Jul 30 19:07:45 2025

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/IFOGSOFT/Augment/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/IFOGSOFT/Augment/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  2.926423s wall, 2.765625s user + 0.187500s system = 2.953125s CPU (100.9%)

RUN-1004 : used memory is 608 MB, reserved memory is 542 MB, peak memory is 720 MB
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
RUN-1001 : open_run syn_1.
RUN-1002 : start command "import_db D:/IFOGSOFT/Augment/IFOG501_2B_20M_PLL_20240316/IFOG501_2B_Runs/syn_1/IFOG501_2B_elaborate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
