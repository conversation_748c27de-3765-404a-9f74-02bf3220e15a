`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	Divider
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	
// Revision 1.01 - File Created
// Additional Comments: 
//Time sequence diagram
//   ___
//  |   |
//__|   |_________________________________________//clk_DA
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate
//0………………………………………………………………………………………………………  242 245
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module Demodulation
#(
	parameter acum_cnt=45,	//Sampling times
	parameter iWID_IN=12,	//AD width
	parameter iWID_OUT=56 	//demodu_data width
)
(
	input						rst_n,
	input						clk, //SYS clock
	input						clk_in, //AD clock
	input						polarity, //flag of transmit-time	
	input						demodulate,
	input						AD_valid,
	input	   [iWID_IN-1:0]	din, //data in
	output reg [iWID_OUT-1:0]	dout  //data out
);

reg							Latch_sum;
reg 		[1:0] 			demodu_dy;		  //demodulate Signal delay register
reg 		[1:0] 			AD_valid_dy;	  //AD_valid Signal delay register
reg			[7:0]			AD_validcnt;         
reg 		[iWID_IN-1:0]	din_reg0;            
reg 		[iWID_IN-1:0]	din_reg1;            
reg  signed	[iWID_OUT-1:0]	sample_sum;           
reg  signed	[iWID_OUT-1:0]	latch_sample_sum;          
reg	 signed	[iWID_OUT-1:0]	median_sum_n;        
reg	 signed	[iWID_OUT-1:0]	INS_dout;     		 
			
reg  signed	[iWID_OUT-1:0]  sample_sum_DY2;   
wire signed	[iWID_OUT-1:0]  sample_sum_DY;  

reg                 Write_en;
reg                 Read_en;
reg        [1:0]    Read_enDY;
wire                empty_flag;
wire                full_flag;

//demodulate:Delay two beats to generate rising edge signal
always @(posedge clk)begin		
	demodu_dy<={demodu_dy[0],demodulate};
end

always @(posedge clk_in)begin			
	AD_valid_dy<={AD_valid_dy[0],AD_valid};   
end


always @(posedge clk_in)begin		
	din_reg0<=din;
	//din_reg1<={~din_reg0[11],din_reg0[10:0]};
	din_reg1<=din_reg0;
end

always @(posedge clk_in)begin	
	if(AD_valid_dy == 2'b01)begin//AD_valid Rising edge
		AD_validcnt <= 8'd1;
	end
	else if(AD_validcnt==acum_cnt+2)begin
		AD_validcnt <= AD_validcnt;
	end
	else begin
		AD_validcnt <= AD_validcnt+1'b1;
	end
end		
	
//sample
always @(posedge clk_in)begin	
	if(AD_valid_dy == 2'b01)begin//AD_valid Rising edge
		sample_sum	<= 56'd0;
	end
	else if(AD_validcnt < acum_cnt)begin
		sample_sum	<= sample_sum+din_reg1;
	end
	else begin
		sample_sum	<= sample_sum;
	end
end

//sample_sum:Delay one beat to make it easy to latch the data
always @(posedge clk_in)begin
	Latch_sum <= (AD_validcnt == acum_cnt);
end

//sample_sum:Delay one beat to make it easy to latch the data
always @(posedge clk_in)begin
	latch_sample_sum <= (Latch_sum)?sample_sum : latch_sample_sum;
end

//sample_sum:Delay one beat to make it easy to latch the data
always @(posedge clk_in)begin
	Write_en <= (AD_validcnt == (acum_cnt+1));
end

//sample_sum:Delay one beat to make it easy to latch the data
always @(posedge clk)begin
	Read_en		<= ~empty_flag;
	Read_enDY	<= {Read_enDY[0],Read_en};
end

Asys_fifo56X16 fifo(
	.rst(~rst_n),  //asynchronous port(),active hight
	.clkw(clk_in),  //write clock
	.clkr(clk),  //read clock
	.we(Write_en),  //write enable(),active hight
	.di(latch_sample_sum),  //write data
	.re(Read_en),  //read enable(),active hight
	.dout(sample_sum_DY),  //read data
	.valid(),  //read data valid flag
	.full_flag(full_flag),  //fifo full flag
	.empty_flag(empty_flag),  //fifo empty flag
	.afull(),  //fifo almost full flag
	.aempty(),  //fifo almost empty flag
	.wrusedw(),  //stored data number in fifo
	.rdusedw() //available data number for read  
);


always @(posedge clk)begin
    sample_sum_DY2 <= (Read_enDY[1] == 1'b1)?sample_sum_DY:sample_sum_DY2;
end

//Calculation increment error
always @(posedge clk)begin
	if(demodu_dy[1:0] == 2'b01)begin
		if(polarity)begin
			//INS_dout<=median_sum_n-sample_sum_DY;
			INS_dout<=sample_sum_DY-median_sum_n;
		end
		else begin
			median_sum_n<=sample_sum;
			INS_dout<=INS_dout;
		end
	end
	else begin		
		INS_dout<=INS_dout;	
		median_sum_n<=median_sum_n;
	end
end		

//INS_dout:Delay one beat to make it easy to latch the data
always @(posedge clk)begin
	dout <= INS_dout;
end
	
endmodule