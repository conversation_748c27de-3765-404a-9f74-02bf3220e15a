============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Mon Mar 18 21:09:58 2024

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 16 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1650 instances
RUN-0007 : 395 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2220 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1661 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1648 instances, 395 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7886, tnet num: 2218, tinst num: 1648, tnode num: 11125, tedge num: 13321.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.192178s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (32.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 604274
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1648.
PHY-3001 : End clustering;  0.000007s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 484491, overlap = 20.25
PHY-3002 : Step(2): len = 448677, overlap = 20.25
PHY-3002 : Step(3): len = 404091, overlap = 20.25
PHY-3002 : Step(4): len = 378841, overlap = 15.75
PHY-3002 : Step(5): len = 369523, overlap = 15.75
PHY-3002 : Step(6): len = 360872, overlap = 18
PHY-3002 : Step(7): len = 349095, overlap = 18
PHY-3002 : Step(8): len = 326646, overlap = 18
PHY-3002 : Step(9): len = 319621, overlap = 20.25
PHY-3002 : Step(10): len = 312505, overlap = 20.25
PHY-3002 : Step(11): len = 297727, overlap = 20.25
PHY-3002 : Step(12): len = 289354, overlap = 20.25
PHY-3002 : Step(13): len = 285811, overlap = 20.25
PHY-3002 : Step(14): len = 271347, overlap = 20.25
PHY-3002 : Step(15): len = 259169, overlap = 20.25
PHY-3002 : Step(16): len = 254109, overlap = 20.25
PHY-3002 : Step(17): len = 249688, overlap = 20.25
PHY-3002 : Step(18): len = 236638, overlap = 20.25
PHY-3002 : Step(19): len = 230269, overlap = 20.25
PHY-3002 : Step(20): len = 227783, overlap = 20.25
PHY-3002 : Step(21): len = 220116, overlap = 20.25
PHY-3002 : Step(22): len = 211521, overlap = 20.25
PHY-3002 : Step(23): len = 208763, overlap = 20.25
PHY-3002 : Step(24): len = 203924, overlap = 20.25
PHY-3002 : Step(25): len = 193452, overlap = 20.25
PHY-3002 : Step(26): len = 189877, overlap = 20.25
PHY-3002 : Step(27): len = 187287, overlap = 20.25
PHY-3002 : Step(28): len = 178962, overlap = 20.25
PHY-3002 : Step(29): len = 174249, overlap = 20.25
PHY-3002 : Step(30): len = 170695, overlap = 20.25
PHY-3002 : Step(31): len = 167679, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133571
PHY-3002 : Step(32): len = 169004, overlap = 15.75
PHY-3002 : Step(33): len = 167727, overlap = 11.25
PHY-3002 : Step(34): len = 164886, overlap = 18
PHY-3002 : Step(35): len = 162374, overlap = 15.75
PHY-3002 : Step(36): len = 157311, overlap = 11.25
PHY-3002 : Step(37): len = 151381, overlap = 6.75
PHY-3002 : Step(38): len = 148682, overlap = 11.25
PHY-3002 : Step(39): len = 145472, overlap = 15.75
PHY-3002 : Step(40): len = 142670, overlap = 9
PHY-3002 : Step(41): len = 136078, overlap = 6.75
PHY-3002 : Step(42): len = 131186, overlap = 13.5
PHY-3002 : Step(43): len = 130039, overlap = 6.75
PHY-3002 : Step(44): len = 126322, overlap = 13.5
PHY-3002 : Step(45): len = 122774, overlap = 11.25
PHY-3002 : Step(46): len = 120762, overlap = 9
PHY-3002 : Step(47): len = 118464, overlap = 9
PHY-3002 : Step(48): len = 117994, overlap = 6.75
PHY-3002 : Step(49): len = 115928, overlap = 6.75
PHY-3002 : Step(50): len = 110607, overlap = 6.75
PHY-3002 : Step(51): len = 109018, overlap = 11.25
PHY-3002 : Step(52): len = 107410, overlap = 11.25
PHY-3002 : Step(53): len = 105508, overlap = 11.25
PHY-3002 : Step(54): len = 105119, overlap = 11.25
PHY-3002 : Step(55): len = 104327, overlap = 6.75
PHY-3002 : Step(56): len = 103488, overlap = 6.75
PHY-3002 : Step(57): len = 99714.7, overlap = 9
PHY-3002 : Step(58): len = 98072.7, overlap = 13.5
PHY-3002 : Step(59): len = 96753.3, overlap = 9
PHY-3002 : Step(60): len = 95998.5, overlap = 5.0625
PHY-3002 : Step(61): len = 94288.5, overlap = 9.5625
PHY-3002 : Step(62): len = 92514.3, overlap = 12.0625
PHY-3002 : Step(63): len = 90894.6, overlap = 14.3125
PHY-3002 : Step(64): len = 89107.1, overlap = 11.9375
PHY-3002 : Step(65): len = 88817.8, overlap = 12.0625
PHY-3002 : Step(66): len = 87815.1, overlap = 12.0625
PHY-3002 : Step(67): len = 83426.5, overlap = 11.75
PHY-3002 : Step(68): len = 81142.2, overlap = 9.875
PHY-3002 : Step(69): len = 80578.6, overlap = 9.875
PHY-3002 : Step(70): len = 79835.9, overlap = 12.3125
PHY-3002 : Step(71): len = 79335.9, overlap = 12.3125
PHY-3002 : Step(72): len = 78482, overlap = 12.5
PHY-3002 : Step(73): len = 75935.7, overlap = 12.25
PHY-3002 : Step(74): len = 75064.3, overlap = 12.1875
PHY-3002 : Step(75): len = 74311.2, overlap = 9.875
PHY-3002 : Step(76): len = 74194.7, overlap = 9.75
PHY-3002 : Step(77): len = 73208.6, overlap = 12.0625
PHY-3002 : Step(78): len = 72146, overlap = 17.1875
PHY-3002 : Step(79): len = 71260.4, overlap = 17.5312
PHY-3002 : Step(80): len = 71271.5, overlap = 15.375
PHY-3002 : Step(81): len = 70735.1, overlap = 13.3438
PHY-3002 : Step(82): len = 69511.3, overlap = 11.0625
PHY-3002 : Step(83): len = 68545, overlap = 13.1562
PHY-3002 : Step(84): len = 68689.4, overlap = 13.2812
PHY-3002 : Step(85): len = 68740, overlap = 13.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000267141
PHY-3002 : Step(86): len = 68585.8, overlap = 10.7812
PHY-3002 : Step(87): len = 68595.7, overlap = 11.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000534282
PHY-3002 : Step(88): len = 68704.4, overlap = 11.125
PHY-3002 : Step(89): len = 68695.5, overlap = 10.875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004292s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039422s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00102975
PHY-3002 : Step(90): len = 71259, overlap = 14.4062
PHY-3002 : Step(91): len = 70379.4, overlap = 13.7188
PHY-3002 : Step(92): len = 68714.8, overlap = 12.7188
PHY-3002 : Step(93): len = 67095.5, overlap = 13.6562
PHY-3002 : Step(94): len = 65762.1, overlap = 13.8438
PHY-3002 : Step(95): len = 64153.7, overlap = 13.5625
PHY-3002 : Step(96): len = 62559.5, overlap = 12.0312
PHY-3002 : Step(97): len = 61115.3, overlap = 13.0938
PHY-3002 : Step(98): len = 59774.5, overlap = 13.6875
PHY-3002 : Step(99): len = 58493.9, overlap = 13.5938
PHY-3002 : Step(100): len = 56883.9, overlap = 13.125
PHY-3002 : Step(101): len = 56075.6, overlap = 13.0625
PHY-3002 : Step(102): len = 55506.9, overlap = 13.4375
PHY-3002 : Step(103): len = 54814.3, overlap = 13.5625
PHY-3002 : Step(104): len = 53992.2, overlap = 11.8125
PHY-3002 : Step(105): len = 53765.9, overlap = 10.375
PHY-3002 : Step(106): len = 53138, overlap = 9.8125
PHY-3002 : Step(107): len = 52548.5, overlap = 9.6875
PHY-3002 : Step(108): len = 52141.9, overlap = 8.5625
PHY-3002 : Step(109): len = 51938.1, overlap = 8.9375
PHY-3002 : Step(110): len = 51557.8, overlap = 8.40625
PHY-3002 : Step(111): len = 51065.7, overlap = 8.65625
PHY-3002 : Step(112): len = 50751.4, overlap = 8.71875
PHY-3002 : Step(113): len = 50751.4, overlap = 8.71875
PHY-3002 : Step(114): len = 50645.4, overlap = 8.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0020595
PHY-3002 : Step(115): len = 50611.3, overlap = 8.53125
PHY-3002 : Step(116): len = 50656.6, overlap = 8.53125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00411901
PHY-3002 : Step(117): len = 50533.9, overlap = 8.59375
PHY-3002 : Step(118): len = 50589, overlap = 8.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.040551s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100126
PHY-3002 : Step(119): len = 51109, overlap = 43.25
PHY-3002 : Step(120): len = 51226.5, overlap = 48.1562
PHY-3002 : Step(121): len = 52026.5, overlap = 46.1562
PHY-3002 : Step(122): len = 52767.6, overlap = 46.8438
PHY-3002 : Step(123): len = 52613.9, overlap = 47.5938
PHY-3002 : Step(124): len = 52275.8, overlap = 47.1875
PHY-3002 : Step(125): len = 51837.6, overlap = 42.2188
PHY-3002 : Step(126): len = 51677.1, overlap = 43.0938
PHY-3002 : Step(127): len = 51506.5, overlap = 43.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000200252
PHY-3002 : Step(128): len = 51772.9, overlap = 41.3438
PHY-3002 : Step(129): len = 52476.8, overlap = 40.1875
PHY-3002 : Step(130): len = 53100.6, overlap = 35.0312
PHY-3002 : Step(131): len = 53431, overlap = 34.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000400503
PHY-3002 : Step(132): len = 53555.3, overlap = 34.7188
PHY-3002 : Step(133): len = 53597.8, overlap = 34.6562
PHY-3002 : Step(134): len = 53659.8, overlap = 36.8125
PHY-3002 : Step(135): len = 54148.5, overlap = 35.75
PHY-3002 : Step(136): len = 54282.2, overlap = 35.1562
PHY-3002 : Step(137): len = 54078.4, overlap = 34.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7886, tnet num: 2218, tinst num: 1648, tnode num: 11125, tedge num: 13321.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.34 peak overflow 2.19
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2220.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 58424, over cnt = 273(0%), over = 1069, worst = 17
PHY-1001 : End global iterations;  0.044286s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (70.6%)

PHY-1001 : Congestion index: top1 = 42.95, top5 = 26.09, top10 = 17.52, top15 = 12.44.
PHY-1001 : End incremental global routing;  0.079705s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (58.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046124s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (33.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.147640s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (52.9%)

OPT-1001 : Current memory(MB): used = 208, reserve = 180, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2220.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 58424, over cnt = 273(0%), over = 1069, worst = 17
PHY-1002 : len = 65400, over cnt = 181(0%), over = 372, worst = 11
PHY-1002 : len = 69576, over cnt = 29(0%), over = 31, worst = 2
PHY-1002 : len = 70040, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 70376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.064096s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 37.16, top5 = 25.85, top10 = 19.03, top15 = 14.15.
OPT-1001 : End congestion update;  0.092685s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.036293s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.130563s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (23.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 183, peak = 212.
OPT-1001 : End physical optimization;  0.458622s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (51.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 395 LUT to BLE ...
SYN-4008 : Packed 395 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 106 SEQ with LUT/SLICE
SYN-4006 : 132 single LUT's are left
SYN-4006 : 697 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1092/1420 primitive instances ...
PHY-3001 : End packing;  0.036356s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (43.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 844 instances
RUN-1001 : 398 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2052 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1495 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 82 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 842 instances, 795 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 54224.8, Over = 66.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6654, tnet num: 2050, tinst num: 842, tnode num: 8987, tedge num: 11680.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.209144s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (74.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.98608e-05
PHY-3002 : Step(138): len = 53546.3, overlap = 68.75
PHY-3002 : Step(139): len = 52922.6, overlap = 70.25
PHY-3002 : Step(140): len = 52627.9, overlap = 71.25
PHY-3002 : Step(141): len = 52225.4, overlap = 72.25
PHY-3002 : Step(142): len = 52142.8, overlap = 70.25
PHY-3002 : Step(143): len = 52070, overlap = 70.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.97216e-05
PHY-3002 : Step(144): len = 52505.7, overlap = 69.25
PHY-3002 : Step(145): len = 52917.4, overlap = 68
PHY-3002 : Step(146): len = 53095.7, overlap = 64.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000119443
PHY-3002 : Step(147): len = 54417.1, overlap = 62.5
PHY-3002 : Step(148): len = 55979.7, overlap = 54.75
PHY-3002 : Step(149): len = 55911.2, overlap = 55.75
PHY-3002 : Step(150): len = 55877.8, overlap = 55.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.064098s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Trial Legalized: Len = 70160.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.032843s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000676829
PHY-3002 : Step(151): len = 67326.8, overlap = 6.25
PHY-3002 : Step(152): len = 64949.3, overlap = 12.5
PHY-3002 : Step(153): len = 63276.3, overlap = 16.25
PHY-3002 : Step(154): len = 61971.3, overlap = 20.25
PHY-3002 : Step(155): len = 61209.1, overlap = 24.25
PHY-3002 : Step(156): len = 60845.2, overlap = 26
PHY-3002 : Step(157): len = 60404, overlap = 26
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00135366
PHY-3002 : Step(158): len = 60716.7, overlap = 25.25
PHY-3002 : Step(159): len = 60807.2, overlap = 25.25
PHY-3002 : Step(160): len = 60826.4, overlap = 23.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00270732
PHY-3002 : Step(161): len = 60927.6, overlap = 24
PHY-3002 : Step(162): len = 60999.2, overlap = 23.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004078s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 65552.7, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003836s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 2, deltaY = 7, maxDist = 2.
PHY-3001 : Final: Len = 65634.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6654, tnet num: 2050, tinst num: 842, tnode num: 8987, tedge num: 11680.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 63/2052.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 72480, over cnt = 131(0%), over = 208, worst = 7
PHY-1002 : len = 73312, over cnt = 82(0%), over = 105, worst = 4
PHY-1002 : len = 74440, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 74600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.071190s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (43.9%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 23.32, top10 = 18.28, top15 = 14.45.
PHY-1001 : End incremental global routing;  0.105784s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (44.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038674s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (40.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.164074s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (38.1%)

OPT-1001 : Current memory(MB): used = 213, reserve = 185, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1810/2052.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 74600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003334s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 23.32, top10 = 18.28, top15 = 14.45.
OPT-1001 : End congestion update;  0.034985s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032845s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (47.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 804 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 842 instances, 795 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65720.8, Over = 0
PHY-3001 : End spreading;  0.003631s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65720.8, Over = 0
PHY-3001 : End incremental legalization;  0.023693s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.9%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.100786s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (62.0%)

OPT-1001 : Current memory(MB): used = 217, reserve = 189, peak = 217.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032278s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1798/2052.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 74712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004749s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 23.34, top10 = 18.29, top15 = 14.47.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032360s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (48.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.558558s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (53.2%)

RUN-1003 : finish command "place" in  3.891255s wall, 1.218750s user + 0.281250s system = 1.500000s CPU (38.5%)

RUN-1004 : used memory is 190 MB, reserved memory is 162 MB, peak memory is 217 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |    off     |       off        |        
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 16 thread(s)
RUN-1001 : There are total 844 instances
RUN-1001 : 398 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2052 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1495 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 82 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6654, tnet num: 2050, tinst num: 842, tnode num: 8987, tedge num: 11680.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 72160, over cnt = 132(0%), over = 209, worst = 8
PHY-1002 : len = 73088, over cnt = 80(0%), over = 102, worst = 4
PHY-1002 : len = 74232, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 74376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.078833s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (19.8%)

PHY-1001 : Congestion index: top1 = 32.13, top5 = 23.38, top10 = 18.20, top15 = 14.38.
PHY-1001 : End global routing;  0.115228s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (13.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 206, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 502, reserve = 479, peak = 502.
PHY-1001 : End build detailed router design. 2.289566s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (43.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33824, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.726614s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (55.9%)

PHY-1001 : Current memory(MB): used = 535, reserve = 512, peak = 535.
PHY-1001 : End phase 1; 0.729894s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (55.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187024, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 513, peak = 536.
PHY-1001 : End initial routed; 0.574652s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (27.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1819(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.087   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.245177s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (57.4%)

PHY-1001 : Current memory(MB): used = 538, reserve = 515, peak = 538.
PHY-1001 : End phase 2; 0.819868s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (36.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187024, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.009638s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187064, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.015634s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187144, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.013462s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (116.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 187176, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.011732s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1819(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.087   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.242839s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (70.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.121380s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (51.5%)

PHY-1001 : Current memory(MB): used = 553, reserve = 531, peak = 553.
PHY-1001 : End phase 3; 0.505261s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (61.8%)

PHY-1003 : Routed, final wirelength = 187176
PHY-1001 : Current memory(MB): used = 553, reserve = 531, peak = 553.
PHY-1001 : End export database. 0.005647s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  4.477937s wall, 2.062500s user + 0.015625s system = 2.078125s CPU (46.4%)

RUN-1003 : finish command "route" in  4.843024s wall, 2.218750s user + 0.015625s system = 2.234375s CPU (46.1%)

RUN-1004 : used memory is 482 MB, reserved memory is 464 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   15
  #output                  18
  #inout                    1

Utilization Statistics
#lut                      843   out of  19600    4.30%
#reg                     1074   out of  19600    5.48%
#le                      1540
  #lut only               466   out of   1540   30.26%
  #reg only               697   out of   1540   45.26%
  #lut&reg                377   out of   1540   24.48%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                               Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                                470
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                                109
#3        wendu/clk_us                    GCLK               mslice             signal_process/ctrl_signal/modulate_reg_syn_39.q0    38
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                                        11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                                1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                                      1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        P74        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1540   |622     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1128   |304     |128     |918     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |35     |29      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |521    |108     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |2       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |84     |28      |21      |80      |0       |1       |
|    rs422                   |Rs422Output                                      |318    |96      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |122    |114     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |21      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |27     |26      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |67     |67      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |76      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1459  
    #2          2       304   
    #3          3       116   
    #4          4        14   
    #5        5-10       85   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6654, tnet num: 2050, tinst num: 842, tnode num: 8987, tedge num: 11680.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2050 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 16 threads.
BIT-1002 : Init instances completely, inst num: 842
BIT-1002 : Init pips with 16 threads.
BIT-1002 : Init pips completely, net num: 2052, pip num: 15041
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 16 threads.
BIT-1003 : Generate bitstream completely, there are 1311 valid insts, and 39664 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  1.600418s wall, 12.796875s user + 0.031250s system = 12.828125s CPU (801.5%)

RUN-1004 : used memory is 515 MB, reserved memory is 494 MB, peak memory is 696 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240318_210958.log"
