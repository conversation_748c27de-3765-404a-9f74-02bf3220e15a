<?xml version="1.0" encoding="UTF-8"?>
<!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
<All_Bram_Infos>
    <Ucode>10111101</Ucode>
    <AL_PHY_BRAM>
        <INST_1>
            <rid>0X0004</rid>
            <wid>0X0004</wid>
            <is_debuggable>n</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>signal_process/demodu/fifo/ram_inst/ramread0_syn_11</name>
            <width_a>18</width_a>
            <width_b>18</width_b>
            <logic_name>signal_process/demodu/fifo/ram_inst/ramread0_syn_10</logic_name>
            <logic_width>55</logic_width>
            <logic_depth>128</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>0</data_offset>
                <depth>128</depth>
                <width>18</width>
                <num_section>1</num_section>
                <section_size>55</section_size>
                <width_per_section>18</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>512</depth>
                    <mode_type>126</mode_type>
                    <width>18</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_1>
        <INST_2>
            <rid>0X0005</rid>
            <wid>0X0005</wid>
            <is_debuggable>n</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>signal_process/demodu/fifo/ram_inst/ramread0_syn_30</name>
            <width_a>18</width_a>
            <width_b>18</width_b>
            <logic_name>signal_process/demodu/fifo/ram_inst/ramread0_syn_10</logic_name>
            <logic_width>55</logic_width>
            <logic_depth>128</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>18</data_offset>
                <depth>128</depth>
                <width>18</width>
                <num_section>1</num_section>
                <section_size>55</section_size>
                <width_per_section>18</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>512</depth>
                    <mode_type>126</mode_type>
                    <width>18</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_2>
        <INST_3>
            <rid>0X0006</rid>
            <wid>0X0006</wid>
            <is_debuggable>n</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>signal_process/demodu/fifo/ram_inst/ramread0_syn_49</name>
            <width_a>18</width_a>
            <width_b>18</width_b>
            <logic_name>signal_process/demodu/fifo/ram_inst/ramread0_syn_10</logic_name>
            <logic_width>55</logic_width>
            <logic_depth>128</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>36</data_offset>
                <depth>128</depth>
                <width>18</width>
                <num_section>1</num_section>
                <section_size>55</section_size>
                <width_per_section>18</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>512</depth>
                    <mode_type>126</mode_type>
                    <width>18</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_3>
        <INST_4>
            <rid>0X0007</rid>
            <wid>0X0007</wid>
            <is_debuggable>n</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>signal_process/demodu/fifo/ram_inst/ramread0_syn_68</name>
            <width_a>18</width_a>
            <width_b>18</width_b>
            <logic_name>signal_process/demodu/fifo/ram_inst/ramread0_syn_10</logic_name>
            <logic_width>55</logic_width>
            <logic_depth>128</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>54</data_offset>
                <depth>128</depth>
                <width>1</width>
                <num_section>1</num_section>
                <section_size>55</section_size>
                <width_per_section>1</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>512</depth>
                    <mode_type>126</mode_type>
                    <width>18</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_4>
    </AL_PHY_BRAM>
</All_Bram_Infos>
