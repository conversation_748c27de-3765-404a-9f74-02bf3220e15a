set_pin_assignment	{ AD_DATA[0] }	{ LOCATION = P19; }
set_pin_assignment	{ AD_DATA[0] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[0] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[0] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[10] }	{ LOCATION = P3; }
set_pin_assignment	{ AD_DATA[10] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[10] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[10] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[11] }	{ LOCATION = P2; }
set_pin_assignment	{ AD_DATA[11] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[11] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[11] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[1] }	{ LOCATION = P18; }
set_pin_assignment	{ AD_DATA[1] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[1] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[1] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[2] }	{ LOCATION = P17; }
set_pin_assignment	{ AD_DATA[2] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[2] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[2] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[3] }	{ LOCATION = P16; }
set_pin_assignment	{ AD_DATA[3] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[3] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[3] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[4] }	{ LOCATION = P14; }
set_pin_assignment	{ AD_DATA[4] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[4] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[4] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[5] }	{ LOCATION = P12; }
set_pin_assignment	{ AD_DATA[5] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[5] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[5] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[6] }	{ LOCATION = P11; }
set_pin_assignment	{ AD_DATA[6] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[6] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[6] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[7] }	{ LOCATION = P10; }
set_pin_assignment	{ AD_DATA[7] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[7] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[7] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[8] }	{ LOCATION = P5; }
set_pin_assignment	{ AD_DATA[8] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[8] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[8] }	{ PACKREG = ON; }
set_pin_assignment	{ AD_DATA[9] }	{ LOCATION = P4; }
set_pin_assignment	{ AD_DATA[9] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ AD_DATA[9] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ AD_DATA[9] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[0] }	{ LOCATION = P60; }
set_pin_assignment	{ DA_DATA[0] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[0] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[0] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[0] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[0] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[10] }	{ LOCATION = P49; }
set_pin_assignment	{ DA_DATA[10] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[10] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[10] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[10] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[10] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[11] }	{ LOCATION = P48; }
set_pin_assignment	{ DA_DATA[11] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[11] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[11] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[11] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[11] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[12] }	{ LOCATION = P47; }
set_pin_assignment	{ DA_DATA[12] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[12] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[12] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[12] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[12] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[13] }	{ LOCATION = P45; }
set_pin_assignment	{ DA_DATA[13] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[13] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[13] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[13] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[13] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[1] }	{ LOCATION = P59; }
set_pin_assignment	{ DA_DATA[1] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[1] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[1] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[1] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[1] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[2] }	{ LOCATION = P57; }
set_pin_assignment	{ DA_DATA[2] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[2] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[2] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[2] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[2] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[3] }	{ LOCATION = P61; }
set_pin_assignment	{ DA_DATA[3] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[3] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[3] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[3] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[3] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[4] }	{ LOCATION = P62; }
set_pin_assignment	{ DA_DATA[4] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[4] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[4] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[4] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[4] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[5] }	{ LOCATION = P63; }
set_pin_assignment	{ DA_DATA[5] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[5] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[5] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[5] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[5] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[6] }	{ LOCATION = P55; }
set_pin_assignment	{ DA_DATA[6] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[6] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[6] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[6] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[6] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[7] }	{ LOCATION = P52; }
set_pin_assignment	{ DA_DATA[7] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[7] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[7] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[7] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[7] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[8] }	{ LOCATION = P51; }
set_pin_assignment	{ DA_DATA[8] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[8] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[8] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[8] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[8] }	{ PACKREG = ON; }
set_pin_assignment	{ DA_DATA[9] }	{ LOCATION = P50; }
set_pin_assignment	{ DA_DATA[9] }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ DA_DATA[9] }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ DA_DATA[9] }	{ PULLTYPE = NONE; }
set_pin_assignment	{ DA_DATA[9] }	{ SLEWRATE = FAST; }
set_pin_assignment	{ DA_DATA[9] }	{ PACKREG = ON; }
set_pin_assignment	{ RXD }	{ LOCATION = P77; }
set_pin_assignment	{ RXD }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ RXD }	{ PULLTYPE = PULLUP; }
set_pin_assignment	{ RxTransmit }	{ LOCATION = P74; }
set_pin_assignment	{ RxTransmit }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ RxTransmit }	{ PULLTYPE = PULLUP; }
set_pin_assignment	{ TXD }	{ LOCATION = P76; }
set_pin_assignment	{ TXD }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ TXD }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ TXD }	{ PULLTYPE = NONE; }
set_pin_assignment	{ TxTransmit }	{ LOCATION = P79; }
set_pin_assignment	{ TxTransmit }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ TxTransmit }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ TxTransmit }	{ PULLTYPE = NONE; }
set_pin_assignment	{ clk_ADo }	{ LOCATION = P13; }
set_pin_assignment	{ clk_ADo }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ clk_ADo }	{ DRIVESTRENGTH = 16; }
set_pin_assignment	{ clk_ADo }	{ PULLTYPE = NONE; }
set_pin_assignment	{ clk_DA }	{ LOCATION = P54; }
set_pin_assignment	{ clk_DA }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ clk_DA }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ clk_DA }	{ PULLTYPE = NONE; }
set_pin_assignment	{ clk_DA }	{ SLEWRATE = FAST; }
set_pin_assignment	{ clk_in }	{ LOCATION = P34; }
set_pin_assignment	{ clk_in }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ clk_in }	{ PULLTYPE = PULLUP; }
set_pin_assignment	{ dq }	{ LOCATION = P87; }
set_pin_assignment	{ dq }	{ IOSTANDARD = LVCMOS33; }
set_pin_assignment	{ dq }	{ DRIVESTRENGTH = 8; }
set_pin_assignment	{ dq }	{ PULLTYPE = PULLUP; }







#set_pin_assignment	{ DA_DATA[0] }	{ LOCATION = P45; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[10] }	{ LOCATION = P60; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[11] }	{ LOCATION = P61; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[12] }	{ LOCATION = P62; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[13] }	{ LOCATION = P63; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[1] }	{ LOCATION = P47; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[2] }	{ LOCATION = P48; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[3] }	{ LOCATION = P49; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[4] }	{ LOCATION = P50; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[5] }	{ LOCATION = P51; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[6] }	{ LOCATION = P52; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[7] }	{ LOCATION = P55; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[8] }	{ LOCATION = P57; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }
#set_pin_assignment	{ DA_DATA[9] }	{ LOCATION = P59; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; }

#FOG501-2B_V1.3主板专用








#ADC：AD9235/GAD2228
#DAC：MS9714
#IO PORT：UART
#IO PORT：CLOCK
#IO PORT：Synchronous signal

#DAC904E
#set_pin_assignment	{ DA_DATA[0]  }	{ LOCATION = P63; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[1]  }	{ LOCATION = P62; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[2]  }	{ LOCATION = P61; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[3]  }	{ LOCATION = P60; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[4]  }	{ LOCATION = P59; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[5]  }	{ LOCATION = P57; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[6]  }	{ LOCATION = P55; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[7]  }	{ LOCATION = P52; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[8]  }	{ LOCATION = P51; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[9]  }	{ LOCATION = P50; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[10] }	{ LOCATION = P49; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[11] }	{ LOCATION = P48; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[12] }	{ LOCATION = P47; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}
#set_pin_assignment	{ DA_DATA[13] }	{ LOCATION = P45; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; PACKREG = ON;}

#set_inst_assignment {CLK120/pll_inst} {location = x0y0z0;} #PLL
