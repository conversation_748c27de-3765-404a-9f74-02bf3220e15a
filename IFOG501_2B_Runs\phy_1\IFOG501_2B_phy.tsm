eagle_s20
13 3038 33 1194 3016 49036 0 0
0.158 0.089 IFOG501_2B eagle_s20 EG4S20NG88 Detail NA 19 8
clock: clk_in
15 0 0 0

clock: CLK120/pll_inst.clkc[0]
23 35862 2206 5
Setup check
33 3
Endpoint: u_uart/U2/tx_data_dy_b[3]_syn_42
33 0.158000 7 3
Timing path: wendu/reg4_syn_121.clk->u_uart/U2/tx_data_dy_b[3]_syn_42
wendu/reg4_syn_121.clk
u_uart/U2/tx_data_dy_b[3]_syn_42
35 0.158000 6.982000 6.824000 3 3
wendu/data[3] u_uart/U2/tx_data_dy_b[3]_syn_49.c[1]
u_uart/U2/tx_data_dy_b[3]_syn_14 u_uart/U2/tx_data_dy_b[3]_syn_46.a[1]
u_uart/U2/tx_data_dy_b[3]_syn_20 u_uart/U2/tx_data_dy_b[3]_syn_42.a[0]

Timing path: wendu/reg4_syn_121.clk->u_uart/U2/tx_data_dy_b[3]_syn_42
wendu/reg4_syn_121.clk
u_uart/U2/tx_data_dy_b[3]_syn_42
75 0.158000 6.982000 6.824000 3 3
wendu/data[3] u_uart/U2/tx_data_dy_b[3]_syn_49.c[1]
u_uart/U2/tx_data_dy_b[3]_syn_14 u_uart/U2/tx_data_dy_b[3]_syn_46.a[0]
u_uart/U2/tx_data_dy_b[3]_syn_20 u_uart/U2/tx_data_dy_b[3]_syn_42.a[0]

Timing path: wendu/reg4_syn_112.clk->u_uart/U2/tx_data_dy_b[3]_syn_42
wendu/reg4_syn_112.clk
u_uart/U2/tx_data_dy_b[3]_syn_42
115 0.314000 6.982000 6.668000 3 3
wendu/data[11] u_uart/U2/tx_data_dy_b[3]_syn_49.d[1]
u_uart/U2/tx_data_dy_b[3]_syn_14 u_uart/U2/tx_data_dy_b[3]_syn_46.a[1]
u_uart/U2/tx_data_dy_b[3]_syn_20 u_uart/U2/tx_data_dy_b[3]_syn_42.a[0]


Endpoint: u_uart/U2/reg0_syn_51
155 0.203000 4 3
Timing path: wendu/reg4_syn_103.clk->u_uart/U2/reg0_syn_51
wendu/reg4_syn_103.clk
u_uart/U2/reg0_syn_51
157 0.203000 6.982000 6.779000 3 3
wendu/data[14] signal_process/rs422/reg5_syn_239.d[0]
u_uart/U2/tx_data_dy_b[6]_syn_7 u_uart/U2/reg1_syn_59.a[0]
u_uart/U2/tx_data_dy_b[6]_syn_13 u_uart/U2/reg0_syn_51.b[1]

Timing path: wendu/reg4_syn_103.clk->u_uart/U2/reg0_syn_51
wendu/reg4_syn_103.clk
u_uart/U2/reg0_syn_51
197 0.245000 6.982000 6.737000 3 3
wendu/data[6] signal_process/rs422/reg5_syn_239.c[0]
u_uart/U2/tx_data_dy_b[6]_syn_7 u_uart/U2/reg1_syn_59.a[0]
u_uart/U2/tx_data_dy_b[6]_syn_13 u_uart/U2/reg0_syn_51.b[1]

Timing path: wendu/reg4_syn_103.clk->u_uart/U2/reg0_syn_51
wendu/reg4_syn_103.clk
u_uart/U2/reg0_syn_51
237 0.328000 6.982000 6.654000 3 3
wendu/data[6] u_uart/U2/reg1_syn_59.d[1]
u_uart/U2/tx_data_dy_b[6]_syn_11 u_uart/U2/reg1_syn_59.b[0]
u_uart/U2/tx_data_dy_b[6]_syn_13 u_uart/U2/reg0_syn_51.b[1]


Endpoint: u_uart/U2/tx_data_dy_b[4]_syn_42
277 0.493000 4 3
Timing path: wendu/reg4_syn_121.clk->u_uart/U2/tx_data_dy_b[4]_syn_42
wendu/reg4_syn_121.clk
u_uart/U2/tx_data_dy_b[4]_syn_42
279 0.493000 6.982000 6.489000 3 3
wendu/data[4] u_uart/U2/tx_data_dy_b[4]_syn_48.d[0]
u_uart/U2/tx_data_dy_b[4]_syn_18 u_uart/U2/tx_data_dy_b[2]_syn_40.b[0]
u_uart/U2/tx_data_dy_b[4]_syn_20 u_uart/U2/tx_data_dy_b[4]_syn_42.a[0]

Timing path: wendu/reg4_syn_109.clk->u_uart/U2/tx_data_dy_b[4]_syn_42
wendu/reg4_syn_109.clk
u_uart/U2/tx_data_dy_b[4]_syn_42
319 0.605000 6.982000 6.377000 3 3
wendu/data[12] u_uart/U2/tx_data_dy_b[4]_syn_44.d[1]
u_uart/U2/tx_data_dy_b[4]_syn_2 u_uart/U2/tx_data_dy_b[4]_syn_42.a[1]
u_uart/U2/tx_data_dy_b[4]_syn_8 u_uart/U2/tx_data_dy_b[4]_syn_42.b[0]

Timing path: wendu/reg4_syn_109.clk->u_uart/U2/tx_data_dy_b[4]_syn_42
wendu/reg4_syn_109.clk
u_uart/U2/tx_data_dy_b[4]_syn_42
359 0.637000 6.982000 6.345000 3 3
wendu/data[12] u_uart/U2/tx_data_dy_b[4]_syn_48.d[1]
u_uart/U2/tx_data_dy_b[4]_syn_14 u_uart/U2/tx_data_dy_b[2]_syn_40.a[0]
u_uart/U2/tx_data_dy_b[4]_syn_20 u_uart/U2/tx_data_dy_b[4]_syn_42.a[0]



Hold check
399 3
Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_30
401 0.256000 7 3
Timing path: signal_process/demodu/fifo/reg0_syn_74.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/fifo/reg0_syn_74.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
403 0.256000 2.183000 2.439000 1 1
signal_process/demodu/fifo/rd_addr[6] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[10]

Timing path: signal_process/demodu/fifo/reg0_syn_80.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/fifo/reg0_syn_80.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
437 0.269000 2.183000 2.452000 1 1
signal_process/demodu/fifo/rd_addr[4] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[8]

Timing path: signal_process/demodu/fifo/reg0_syn_77.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/fifo/reg0_syn_77.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
471 0.290000 2.183000 2.473000 1 1
signal_process/demodu/fifo/rd_addr[5] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[9]


Endpoint: signal_process/demodu/reg9_syn_238
505 0.258000 1 1
Timing path: signal_process/demodu/reg11_syn_212.clk->signal_process/demodu/reg9_syn_238
signal_process/demodu/reg11_syn_212.clk
signal_process/demodu/reg9_syn_238
507 0.258000 2.191000 2.449000 0 1
signal_process/demodu/sample_sum[43] signal_process/demodu/reg9_syn_238.mi[1]


Endpoint: signal_process/rs422/reg4_syn_304
541 0.260000 1 1
Timing path: signal_process/integ/reg1_syn_237.clk->signal_process/rs422/reg4_syn_304
signal_process/integ/reg1_syn_237.clk
signal_process/rs422/reg4_syn_304
543 0.260000 2.107000 2.367000 0 1
signal_process/ang_vel_data[44] signal_process/rs422/reg4_syn_304.mi[1]



Recovery check
577 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36
579 6.630000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36
581 6.630000 10.245000 3.615000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_36.sr


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
615 6.681000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
617 6.681000 10.245000 3.564000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.sr


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_68
651 6.727000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_68
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_68
653 6.727000 10.295000 3.568000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/ramread0_syn_68.rstb



Removal check
687 3
Endpoint: signal_process/demodu/fifo/reg0_syn_80
689 0.127000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/reg0_syn_80
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/reg0_syn_80
691 0.127000 2.220000 2.347000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/reg0_syn_80.sr


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_11
725 0.221000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
727 0.221000 2.233000 2.454000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/ramread0_syn_11.rstb


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42
761 0.235000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42
763 0.235000 2.236000 2.471000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg2_syn_42.sr



Period check
797 1
Endpoint: signal_process/modu/mult0_syn_2.clk
801 4.919000 1 0



clock: CLK120/pll_inst.clkc[3]
802 5474 574 4
Setup check
812 3
Endpoint: signal_process/demodu/reg0_syn_13
812 6.940000 1 1
Timing path: signal_process/demodu/reg7_syn_275.clk->signal_process/demodu/reg0_syn_13
signal_process/demodu/reg7_syn_275.clk
signal_process/demodu/reg0_syn_13
814 6.940000 10.383000 3.443000 0 1
signal_process/ctrl_signal/AD_valid signal_process/demodu/reg0_syn_13.mi[0]


Endpoint: signal_process/demodu/reg11_syn_215
848 11.340000 57 3
Timing path: signal_process/demodu/reg6_syn_40.clk->signal_process/demodu/reg11_syn_215
signal_process/demodu/reg6_syn_40.clk
signal_process/demodu/reg11_syn_215
850 11.340000 18.896000 7.556000 2 12
signal_process/demodu/din_reg1[6] signal_process/demodu/add1_syn_324.e[1]
signal_process/demodu/add1_syn_274 signal_process/demodu/add1_syn_325.fci
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/sample_sum_b2[44] signal_process/demodu/reg11_syn_215.mi[1]

Timing path: signal_process/demodu/reg6_syn_45.clk->signal_process/demodu/reg11_syn_215
signal_process/demodu/reg6_syn_45.clk
signal_process/demodu/reg11_syn_215
906 11.381000 18.896000 7.515000 2 11
signal_process/demodu/din_reg1[8] signal_process/demodu/add1_syn_325.e[0]
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/sample_sum_b2[44] signal_process/demodu/reg11_syn_215.mi[1]

Timing path: signal_process/demodu/reg6_syn_47.clk->signal_process/demodu/reg11_syn_215
signal_process/demodu/reg6_syn_47.clk
signal_process/demodu/reg11_syn_215
960 11.533000 18.896000 7.363000 2 11
signal_process/demodu/din_reg1[7] signal_process/demodu/add1_syn_325.d[0]
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/sample_sum_b2[44] signal_process/demodu/reg11_syn_215.mi[1]


Endpoint: signal_process/demodu/reg11_syn_203
1014 11.392000 65 3
Timing path: signal_process/demodu/reg6_syn_40.clk->signal_process/demodu/reg11_syn_203
signal_process/demodu/reg6_syn_40.clk
signal_process/demodu/reg11_syn_203
1016 11.392000 18.896000 7.504000 2 14
signal_process/demodu/din_reg1[6] signal_process/demodu/add1_syn_324.e[1]
signal_process/demodu/add1_syn_274 signal_process/demodu/add1_syn_325.fci
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_203.mi[1]

Timing path: signal_process/demodu/reg6_syn_45.clk->signal_process/demodu/reg11_syn_203
signal_process/demodu/reg6_syn_45.clk
signal_process/demodu/reg11_syn_203
1076 11.433000 18.896000 7.463000 2 13
signal_process/demodu/din_reg1[8] signal_process/demodu/add1_syn_325.e[0]
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_203.mi[1]

Timing path: signal_process/demodu/reg6_syn_47.clk->signal_process/demodu/reg11_syn_203
signal_process/demodu/reg6_syn_47.clk
signal_process/demodu/reg11_syn_203
1134 11.585000 18.896000 7.311000 2 13
signal_process/demodu/din_reg1[7] signal_process/demodu/add1_syn_325.d[0]
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_203.mi[1]



Hold check
1192 3
Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1194 0.089000 9 3
Timing path: signal_process/demodu/reg8_syn_215.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_49
signal_process/demodu/reg8_syn_215.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1196 0.089000 2.274000 2.363000 1 1
signal_process/demodu/latch_sample_sum[43] signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7]

Timing path: signal_process/demodu/reg8_syn_218.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_49
signal_process/demodu/reg8_syn_218.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1230 0.196000 2.274000 2.470000 1 1
signal_process/demodu/latch_sample_sum[44] signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8]

Timing path: signal_process/demodu/reg8_syn_221.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_49
signal_process/demodu/reg8_syn_221.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1264 0.205000 2.274000 2.479000 1 1
signal_process/demodu/latch_sample_sum[39] signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1298 0.089000 9 3
Timing path: signal_process/demodu/reg8_syn_179.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/reg8_syn_179.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1300 0.089000 2.274000 2.363000 1 1
signal_process/demodu/latch_sample_sum[16] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7]

Timing path: signal_process/demodu/reg8_syn_176.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/reg8_syn_176.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1334 0.114000 2.274000 2.388000 1 1
signal_process/demodu/latch_sample_sum[17] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8]

Timing path: signal_process/demodu/reg8_syn_191.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/reg8_syn_191.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1368 0.186000 2.274000 2.460000 1 1
signal_process/demodu/latch_sample_sum[9] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1402 0.089000 9 3
Timing path: signal_process/demodu/reg8_syn_197.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/reg8_syn_197.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1404 0.089000 2.274000 2.363000 1 1
signal_process/demodu/latch_sample_sum[5] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5]

Timing path: signal_process/demodu/reg8_syn_191.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/reg8_syn_191.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1438 0.089000 2.274000 2.363000 1 1
signal_process/demodu/latch_sample_sum[3] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3]

Timing path: signal_process/demodu/reg8_syn_200.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/reg8_syn_200.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
1472 0.089000 2.274000 2.363000 1 1
signal_process/demodu/latch_sample_sum[1] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1]



Recovery check
1506 3
Endpoint: signal_process/demodu/fifo/full_flag_reg_syn_5
1508 15.046000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/full_flag_reg_syn_5
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/full_flag_reg_syn_5
1510 15.046000 18.712000 3.666000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/full_flag_reg_syn_5.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26
1544 15.051000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26
1546 15.051000 18.712000 3.661000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_26.sr


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39
1580 15.188000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39
1582 15.188000 18.712000 3.524000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.sr



Removal check
1616 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36
1618 0.246000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36
1620 0.246000 2.327000 2.573000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36
1654 0.285000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36
1656 0.285000 2.327000 2.612000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.sr


Endpoint: signal_process/demodu/fifo/reg1_syn_75
1690 0.285000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/reg1_syn_75
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/reg1_syn_75
1692 0.285000 2.327000 2.612000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/reg1_syn_75.sr




clock: CLK120/pll_inst.clkc[4]
1728 0 0 0

clock: clk_us
1736 7662 198 2
Setup check
1746 3
Endpoint: dq_syn_2
1746 992.375000 131 3
Timing path: wendu/reg2_syn_129.clk->dq_syn_2
wendu/reg2_syn_129.clk
dq_syn_2
1748 992.375000 1003.194000 10.819000 5 14
wendu/cnt_us[3] wendu/lt0_syn_90.a[1]
wendu/lt0_syn_10 wendu/lt0_syn_93.fci
wendu/lt0_syn_14 wendu/lt0_syn_96.fci
wendu/lt0_syn_18 wendu/lt0_syn_99.fci
wendu/lt0_syn_22 wendu/lt0_syn_102.fci
wendu/lt0_syn_26 wendu/lt0_syn_105.fci
wendu/lt0_syn_30 wendu/lt0_syn_108.fci
wendu/lt0_syn_34 wendu/lt0_syn_111.fci
wendu/lt0_syn_38 wendu/lt0_syn_114.fci
wendu/lt0_syn_42 wendu/lt0_syn_116.fci
wendu/data_temp_b2_n wendu/next_state[0]_syn_48.a[0]
wendu/cur_state[0]_syn_3584 wendu/cur_state[0]_syn_3761.a[1]
wendu/cur_state[0]_syn_3586 wendu/cur_state[0]_syn_3769.b[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts

Timing path: wendu/reg2_syn_142.clk->dq_syn_2
wendu/reg2_syn_142.clk
dq_syn_2
1812 992.416000 1003.194000 10.778000 5 13
wendu/cnt_us[4] wendu/lt0_syn_93.a[0]
wendu/lt0_syn_14 wendu/lt0_syn_96.fci
wendu/lt0_syn_18 wendu/lt0_syn_99.fci
wendu/lt0_syn_22 wendu/lt0_syn_102.fci
wendu/lt0_syn_26 wendu/lt0_syn_105.fci
wendu/lt0_syn_30 wendu/lt0_syn_108.fci
wendu/lt0_syn_34 wendu/lt0_syn_111.fci
wendu/lt0_syn_38 wendu/lt0_syn_114.fci
wendu/lt0_syn_42 wendu/lt0_syn_116.fci
wendu/data_temp_b2_n wendu/next_state[0]_syn_48.a[0]
wendu/cur_state[0]_syn_3584 wendu/cur_state[0]_syn_3761.a[1]
wendu/cur_state[0]_syn_3586 wendu/cur_state[0]_syn_3769.b[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts

Timing path: wendu/reg2_syn_145.clk->dq_syn_2
wendu/reg2_syn_145.clk
dq_syn_2
1874 992.490000 1003.194000 10.704000 5 14
wendu/cnt_us[2] wendu/lt0_syn_90.a[0]
wendu/lt0_syn_10 wendu/lt0_syn_93.fci
wendu/lt0_syn_14 wendu/lt0_syn_96.fci
wendu/lt0_syn_18 wendu/lt0_syn_99.fci
wendu/lt0_syn_22 wendu/lt0_syn_102.fci
wendu/lt0_syn_26 wendu/lt0_syn_105.fci
wendu/lt0_syn_30 wendu/lt0_syn_108.fci
wendu/lt0_syn_34 wendu/lt0_syn_111.fci
wendu/lt0_syn_38 wendu/lt0_syn_114.fci
wendu/lt0_syn_42 wendu/lt0_syn_116.fci
wendu/data_temp_b2_n wendu/next_state[0]_syn_48.a[0]
wendu/cur_state[0]_syn_3584 wendu/cur_state[0]_syn_3761.a[1]
wendu/cur_state[0]_syn_3586 wendu/cur_state[0]_syn_3769.b[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts


Endpoint: wendu/reg2_syn_129
1938 993.726000 44 3
Timing path: wendu/reg2_syn_139.clk->wendu/reg2_syn_129
wendu/reg2_syn_139.clk
wendu/reg2_syn_129
1940 993.726000 1003.266000 9.540000 6 6
wendu/cnt_us[10] wendu/cur_state[0]_syn_3786.c[1]
wendu/cur_state[0]_syn_3523 wendu/cur_state[0]_syn_3756.a[0]
wendu/cur_state[0]_syn_3529 wendu/cur_state[0]_syn_3792.a[1]
wendu/cur_state[0]_syn_3531 wendu/cur_state[0]_syn_3812.d[1]
wendu/cur_state[2]_syn_1562 wendu/cur_state[0]_syn_3789.d[1]
wendu/cur_state[0]_syn_3534 wendu/reg2_syn_129.d[1]

Timing path: wendu/reg2_syn_142.clk->wendu/reg2_syn_129
wendu/reg2_syn_142.clk
wendu/reg2_syn_129
1988 993.950000 1003.266000 9.316000 6 6
wendu/cnt_us[4] wendu/cur_state[0]_syn_3786.a[1]
wendu/cur_state[0]_syn_3523 wendu/cur_state[0]_syn_3756.a[0]
wendu/cur_state[0]_syn_3529 wendu/cur_state[0]_syn_3792.a[1]
wendu/cur_state[0]_syn_3531 wendu/cur_state[0]_syn_3812.d[1]
wendu/cur_state[2]_syn_1562 wendu/cur_state[0]_syn_3789.d[1]
wendu/cur_state[0]_syn_3534 wendu/reg2_syn_129.d[1]

Timing path: wendu/reg2_syn_148.clk->wendu/reg2_syn_129
wendu/reg2_syn_148.clk
wendu/reg2_syn_129
2036 994.198000 1003.266000 9.068000 6 6
wendu/cnt_us[7] wendu/cur_state[0]_syn_3786.b[1]
wendu/cur_state[0]_syn_3523 wendu/cur_state[0]_syn_3756.a[0]
wendu/cur_state[0]_syn_3529 wendu/cur_state[0]_syn_3792.a[1]
wendu/cur_state[0]_syn_3531 wendu/cur_state[0]_syn_3812.d[1]
wendu/cur_state[2]_syn_1562 wendu/cur_state[0]_syn_3789.d[1]
wendu/cur_state[0]_syn_3534 wendu/reg2_syn_129.d[1]


Endpoint: wendu/reg2_syn_135
2084 993.726000 41 3
Timing path: wendu/reg2_syn_139.clk->wendu/reg2_syn_135
wendu/reg2_syn_139.clk
wendu/reg2_syn_135
2086 993.726000 1003.266000 9.540000 6 6
wendu/cnt_us[10] wendu/cur_state[0]_syn_3786.c[1]
wendu/cur_state[0]_syn_3523 wendu/cur_state[0]_syn_3756.a[0]
wendu/cur_state[0]_syn_3529 wendu/cur_state[0]_syn_3792.a[1]
wendu/cur_state[0]_syn_3531 wendu/cur_state[0]_syn_3812.d[1]
wendu/cur_state[2]_syn_1562 wendu/cur_state[0]_syn_3789.d[1]
wendu/cur_state[0]_syn_3534 wendu/reg2_syn_135.d[0]

Timing path: wendu/reg2_syn_142.clk->wendu/reg2_syn_135
wendu/reg2_syn_142.clk
wendu/reg2_syn_135
2134 993.950000 1003.266000 9.316000 6 6
wendu/cnt_us[4] wendu/cur_state[0]_syn_3786.a[1]
wendu/cur_state[0]_syn_3523 wendu/cur_state[0]_syn_3756.a[0]
wendu/cur_state[0]_syn_3529 wendu/cur_state[0]_syn_3792.a[1]
wendu/cur_state[0]_syn_3531 wendu/cur_state[0]_syn_3812.d[1]
wendu/cur_state[2]_syn_1562 wendu/cur_state[0]_syn_3789.d[1]
wendu/cur_state[0]_syn_3534 wendu/reg2_syn_135.d[0]

Timing path: wendu/reg2_syn_148.clk->wendu/reg2_syn_135
wendu/reg2_syn_148.clk
wendu/reg2_syn_135
2182 994.198000 1003.266000 9.068000 6 6
wendu/cnt_us[7] wendu/cur_state[0]_syn_3786.b[1]
wendu/cur_state[0]_syn_3523 wendu/cur_state[0]_syn_3756.a[0]
wendu/cur_state[0]_syn_3529 wendu/cur_state[0]_syn_3792.a[1]
wendu/cur_state[0]_syn_3531 wendu/cur_state[0]_syn_3812.d[1]
wendu/cur_state[2]_syn_1562 wendu/cur_state[0]_syn_3789.d[1]
wendu/cur_state[0]_syn_3534 wendu/reg2_syn_135.d[0]



Hold check
2230 3
Endpoint: wendu/reg5_syn_118
2232 0.434000 49 3
Timing path: wendu/reg5_syn_118.clk->wendu/reg5_syn_118
wendu/reg5_syn_118.clk
wendu/reg5_syn_118
2234 0.434000 2.830000 3.264000 1 1
wendu/data_temp[6] wendu/reg5_syn_118.d[1]

Timing path: wendu/reg5_syn_118.clk->wendu/reg5_syn_118
wendu/reg5_syn_118.clk
wendu/reg5_syn_118
2272 0.699000 2.830000 3.529000 1 1
wendu/data_temp[5] wendu/reg5_syn_118.c[1]

Timing path: wendu/cur_state[0]_syn_3775.clk->wendu/reg5_syn_118
wendu/cur_state[0]_syn_3775.clk
wendu/reg5_syn_118
2310 1.276000 2.830000 4.106000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3797.c[1]
wendu/cur_state[0]_syn_3556 wendu/reg5_syn_118.a[1]


Endpoint: wendu/reg5_syn_100
2350 0.443000 49 3
Timing path: wendu/reg5_syn_121.clk->wendu/reg5_syn_100
wendu/reg5_syn_121.clk
wendu/reg5_syn_100
2352 0.443000 2.875000 3.318000 1 1
wendu/data_temp[15] wendu/reg5_syn_100.d[0]

Timing path: wendu/reg5_syn_100.clk->wendu/reg5_syn_100
wendu/reg5_syn_100.clk
wendu/reg5_syn_100
2390 0.814000 2.875000 3.689000 1 1
wendu/data_temp[14] wendu/reg5_syn_100.c[0]

Timing path: wendu/cur_state[0]_syn_3775.clk->wendu/reg5_syn_100
wendu/cur_state[0]_syn_3775.clk
wendu/reg5_syn_100
2428 1.216000 2.875000 4.091000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3807.c[0]
wendu/cur_state[0]_syn_3558 wendu/reg5_syn_100.b[0]


Endpoint: wendu/reg5_syn_121
2468 0.451000 49 3
Timing path: wendu/reg5_syn_109.clk->wendu/reg5_syn_121
wendu/reg5_syn_109.clk
wendu/reg5_syn_121
2470 0.451000 2.875000 3.326000 1 1
wendu/data_temp[1] wendu/reg5_syn_121.d[1]

Timing path: wendu/reg5_syn_121.clk->wendu/reg5_syn_121
wendu/reg5_syn_121.clk
wendu/reg5_syn_121
2508 0.611000 2.875000 3.486000 1 1
wendu/data_temp[0] wendu/reg5_syn_121.c[1]

Timing path: wendu/cur_state[0]_syn_3775.clk->wendu/reg5_syn_121
wendu/cur_state[0]_syn_3775.clk
wendu/reg5_syn_121
2546 1.225000 2.875000 4.100000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3807.c[0]
wendu/cur_state[0]_syn_3558 wendu/reg5_syn_121.b[1]




Set input delay: 14.5ns max, and 14.5ns min. 
2586 24 24 2
Setup check
2596 3
Endpoint: AD_DATA[0]_syn_4
2596 3.080000 1 1
Timing path: AD_DATA[0]->AD_DATA[0]_syn_4
AD_DATA[0]
AD_DATA[0]_syn_4
2598 3.080000 18.804000 15.724000 0 1
AD_DATA[0] AD_DATA[0]_syn_4.ipad


Endpoint: AD_DATA[1]_syn_4
2630 3.080000 1 1
Timing path: AD_DATA[1]->AD_DATA[1]_syn_4
AD_DATA[1]
AD_DATA[1]_syn_4
2632 3.080000 18.804000 15.724000 0 1
AD_DATA[1] AD_DATA[1]_syn_4.ipad


Endpoint: AD_DATA[2]_syn_4
2664 3.080000 1 1
Timing path: AD_DATA[2]->AD_DATA[2]_syn_4
AD_DATA[2]
AD_DATA[2]_syn_4
2666 3.080000 18.804000 15.724000 0 1
AD_DATA[2] AD_DATA[2]_syn_4.ipad



Hold check
2698 3
Endpoint: AD_DATA[0]_syn_4
2700 13.126000 1 1
Timing path: AD_DATA[0]->AD_DATA[0]_syn_4
AD_DATA[0]
AD_DATA[0]_syn_4
2702 13.126000 2.226000 15.352000 0 1
AD_DATA[0] AD_DATA[0]_syn_4.ipad


Endpoint: AD_DATA[1]_syn_4
2734 13.126000 1 1
Timing path: AD_DATA[1]->AD_DATA[1]_syn_4
AD_DATA[1]
AD_DATA[1]_syn_4
2736 13.126000 2.226000 15.352000 0 1
AD_DATA[1] AD_DATA[1]_syn_4.ipad


Endpoint: AD_DATA[2]_syn_4
2768 13.126000 1 1
Timing path: AD_DATA[2]->AD_DATA[2]_syn_4
AD_DATA[2]
AD_DATA[2]_syn_4
2770 13.126000 2.226000 15.352000 0 1
AD_DATA[2] AD_DATA[2]_syn_4.ipad




Path delay: 8.032ns max
2802 7 7 1
Max path
2812 3
Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33
2812 7.101000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_42.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_42.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33
2814 7.101000 10.192000 3.091000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[4] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_33.mi[1]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28
2847 7.148000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_24.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_24.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28
2849 7.148000 10.192000 3.044000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[6] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_28.mi[0]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29
2882 7.151000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29
2884 7.151000 10.192000 3.041000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[1] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_29.mi[1]




Path delay: 8.032ns max
2919 7 7 1
Max path
2929 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
2929 7.025000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
2931 7.025000 10.326000 3.301000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[2] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[0]


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
2964 7.145000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_36.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
2966 7.145000 10.326000 3.181000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[5] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.mi[0]


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31
2999 7.145000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31
3001 7.145000 10.326000 3.181000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[0] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg1_syn_31.mi[0]





Timing group statistics: 
	Clock constraints: 
	  Clock Name                                  Min Period     Max Freq           Skew      Fanout            TNS
	  CLK120/pll_inst.clkc[0] (120.0MHz)             8.175ns     122.324MHz        0.480ns       465        0.000ns
	  CLK120/pll_inst.clkc[3] (60.0MHz)             13.586ns      73.605MHz        0.326ns       109        0.000ns
	  clk_us (1000.0KHz)                             7.625ns     131.148MHz        0.254ns        40        0.000ns
	Minimum input arrival time before clock: 1.224ns
	Maximum output required time after clock: no constraint path
	Maximum combinational path delay: no constraint path
Warning: No clock constraint on 1 clock net(s): 
	dlycnt_n1_syn_2

	Exceptions:

		Check Type:	MIN
		----------------------------------------------------------------------------------------------------
		       Path Num     Constraint                                                                      
		              7     set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
		              7     set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]

