/************************************************************\
 **     Copyright (c) 2012-2023 Anlogic Inc.
 **  All Right Reserved.\
\************************************************************/
/************************************************************\
 ** Log	:	This file is generated by Anlogic IP Generator.
 ** File	:	D:/IFOGSOFT/IFOG501_2B_20M_PLL_20240316/al_ip/global_clock.v
 ** Date	:	2024 03 16
 ** TD version	:	5.6.71036
\************************************************************/

///////////////////////////////////////////////////////////////////////////////
//	Input frequency:             20.000MHz
//	Clock multiplication factor: 48
//	Clock division factor:       1
//	Clock information:
//		Clock name	| Frequency 	| Phase shift
//		C0        	| 120.000000MHZ	| 0  DEG     
//		C3        	| 60.000000 MHZ	| 0  DEG     
//		C4        	| 60.000000 MHZ	| 0  DEG     
///////////////////////////////////////////////////////////////////////////////
`timescale 1 ns / 100 fs

module global_clock (
  refclk,
  reset,
  extlock,
  clk0_out,
  clk3_out,
  clk4_out 
);

  input refclk;
  input reset;
  output extlock;
  output clk0_out;
  output clk3_out;
  output clk4_out;


  EG_PHY_PLL #(
    .DPHASE_SOURCE("DISABLE"),
    .DYNCFG("DISABLE"),
    .FIN("20.000"),
    .FEEDBK_MODE("NOCOMP"),
    .FEEDBK_PATH("VCO_PHASE_0"),
    .STDBY_ENABLE("DISABLE"),
    .PLLRST_ENA("ENABLE"),
    .SYNC_ENABLE("DISABLE"),
    .GMC_GAIN(2),
    .ICP_CURRENT(9),
    .KVCO(2),
    .LPF_CAPACITOR(1),
    .LPF_RESISTOR(8),
    .REFCLK_DIV(1),
    .FBCLK_DIV(48),
    .CLKC0_ENABLE("ENABLE"),
    .CLKC0_DIV(8),
    .CLKC0_CPHASE(7),
    .CLKC0_FPHASE(0),
    .CLKC3_ENABLE("ENABLE"),
    .CLKC3_DIV(16),
    .CLKC3_CPHASE(15),
    .CLKC3_FPHASE(0),
    .CLKC4_ENABLE("ENABLE"),
    .CLKC4_DIV(16),
    .CLKC4_CPHASE(15),
    .CLKC4_FPHASE(0) 
  ) pll_inst (
    .refclk(refclk),
    .reset(reset),
    .stdby(1'b0),
    .extlock(extlock),
    .load_reg(1'b0),
    .psclk(1'b0),
    .psdown(1'b0),
    .psstep(1'b0),
    .psclksel(3'b000),
    .psdone(open),
    .dclk(1'b0),
    .dcs(1'b0),
    .dwe(1'b0),
    .di(8'b00000000),
    .daddr(6'b000000),
    .do({open, open, open, open, open, open, open, open}),
    .fbclk(1'b0),
    .clkc({clk4_out, clk3_out, open, open, clk0_out}) 
  );

endmodule

