create_clock -name clk_in -period 50 -waveform {0 25} [get_ports {clk_in}]
#create_clock -name clk4p5m -period 222 -waveform {0 111} [get_nets {clk4p5m}]
create_clock -name clk_us -period 1000 -waveform {0 500} [get_nets {wendu/clk_us}]
 
derive_pll_clocks

create_clock -name clk_vir -period 16.667  

set_input_delay -clock clk_vir  14.5 [get_ports {AD_DATA[*]}]

set_clock_groups -exclusive -group [get_clocks clk_in]
#set_output_delay -clock clk_vir  2 [get_ports {clk_ADo}]
#set_input_delay -clock [get_clocks {clk_in}]  3 [all_inputs]
#set_output_delay -clock [get_clocks {clk_in}] 3 [all_outputs]
#set_false_path -from [get_clocks {CLK120/pll_inst.clkc[0]}] -to [get_clocks {CLK120/pll_inst.clkc[1]}]
#set_false_path -from [get_clocks {CLK120/pll_inst.clkc[1]}] -to [get_clocks {CLK120/pll_inst.clkc[0]}]