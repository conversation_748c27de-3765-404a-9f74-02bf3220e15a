`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/26/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	Ctrl_Data 
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	处理arm标定后的数据
// Revision 1.01 - File Created
// Additional Comments: 
//// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module Ctrl_Data
#(
	parameter iWID_RS422=32  //RS422 DATA width
)
(
    input 					clk,		//80Mhz
    input 					rst_n,    
    input 					rd_done,    //温度数据读完
    input 					transmit, 	//rs422数据已经准备好
	 input					tx_done,	//串口8bit数据发送完成
	 input [iWID_RS422-1:0] data_Packet,//数据来自FMC总线，需要发数据
	 input [15:0] 			temp_data,  //转换后得到的温度数据   
	 output reg 			tx_wr,		//发送串口数据状态位
	 output reg 	[7:0] 	tx_data  	//转化8bit串口发送的数据
);
reg [7:0] 	tx_data_dy;
reg [3:0] 	tx_state;
localparam	tx_idle 	= 4'h0 ;
localparam	tx_Header1 	= 4'h1 ;
localparam	tx_Header2 	= 4'h2 ;
localparam	tx_First 	= 4'h3 ;
localparam	tx_Second 	= 4'h4 ;
localparam	tx_Third 	= 4'h5 ;
localparam	tx_Fourth 	= 4'h6 ;
localparam	tx_Fifth 	= 4'h7 ;
localparam	tx_Sixth 	= 4'h8 ;
localparam	tx_Seventh 	= 4'h9 ;
localparam	tx_end 		= 4'hA ;

always @(posedge clk or negedge rst_n)begin
    if(rst_n == 1'b0)begin
        tx_wr 		<= 1'b0;
        tx_state 	<= tx_idle;
		tx_data_dy 	<= 8'h00;
    end
	else begin
		case(tx_state)
			tx_idle:begin
						tx_wr <= 1'b0;
						if(transmit == 1'b1)//rs422数据准备好						
							tx_state <= tx_Header1;
						else 
							tx_state <= tx_idle;					
					end
			tx_Header1:begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= 8'hA5;
							tx_state 	<= tx_Header2;	
					end
			tx_Header2:begin
								if(tx_done == 1'b1)begin
									tx_wr 		<= 1'b1;
									tx_data_dy 	<= 8'hA5;
									tx_state	<= tx_First;
								end
								else begin
									tx_wr 		<= 1'b0;
									tx_data_dy 	<= tx_data_dy;
									tx_state 	<= tx_Header2;							
								end
					end
			tx_First:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= data_Packet[31:24];
							tx_state 	<= tx_Second;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_First;												
						end
					end
			tx_Second:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= data_Packet[23:16];
							tx_state 	<= tx_Third;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Second;												
						end
					end	
			tx_Third:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= data_Packet[15:8];
							tx_state 	<= tx_Fourth;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Third;												
						end
					end	
			tx_Fourth:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= data_Packet[7:0];
							tx_state 	<= tx_Fifth;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Fourth;												
						end
					end	
/* 			tx_Fifth:begin
						if(tx_done == 1'b1)begin
							tx_wr <= 1'b1;
							tx_data_dy <= { data_Packet[31]^data_Packet[23]^data_Packet[15]^data_Packet[7],
											data_Packet[30]^data_Packet[22]^data_Packet[14]^data_Packet[6],
											data_Packet[29]^data_Packet[21]^data_Packet[13]^data_Packet[5],
											data_Packet[28]^data_Packet[20]^data_Packet[12]^data_Packet[4],
											data_Packet[27]^data_Packet[19]^data_Packet[11]^data_Packet[3],
											data_Packet[26]^data_Packet[18]^data_Packet[10]^data_Packet[2],
											data_Packet[25]^data_Packet[17]^data_Packet[9]^data_Packet[1],
											data_Packet[24]^data_Packet[16]^data_Packet[8]^data_Packet[0]
											};
							tx_state <= tx_Sixth;		
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Fifth;												
						end
					end
			tx_Sixth:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= temp_data[15:8];
							tx_state 	<= tx_Seventh;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Sixth;												
						end
					end
			tx_Seventh:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= temp_data[7:0];
							tx_state 	<= tx_end;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Seventh;												
						end
					end */		
 			tx_Fifth:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= temp_data[15:8];
							tx_state 	<= tx_Sixth;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Fifth;												
						end
					end
			tx_Sixth:begin
						if(tx_done == 1'b1)begin
							tx_wr 		<= 1'b1;
							tx_data_dy 	<= temp_data[7:0];
							tx_state 	<= tx_Seventh;	
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Sixth;												
						end
					end	
			tx_Seventh:begin
						if(tx_done == 1'b1)begin
							tx_wr <= 1'b1;
							tx_data_dy <= { data_Packet[31]^data_Packet[23]^data_Packet[15]^data_Packet[7]^temp_data[15]^temp_data[7],
											data_Packet[30]^data_Packet[22]^data_Packet[14]^data_Packet[6]^temp_data[14]^temp_data[6],
											data_Packet[29]^data_Packet[21]^data_Packet[13]^data_Packet[5]^temp_data[13]^temp_data[5],
											data_Packet[28]^data_Packet[20]^data_Packet[12]^data_Packet[4]^temp_data[12]^temp_data[4],
											data_Packet[27]^data_Packet[19]^data_Packet[11]^data_Packet[3]^temp_data[11]^temp_data[3],
											data_Packet[26]^data_Packet[18]^data_Packet[10]^data_Packet[2]^temp_data[10]^temp_data[2],
											data_Packet[25]^data_Packet[17]^data_Packet[9]^data_Packet[1]^temp_data[9]^temp_data[1],
											data_Packet[24]^data_Packet[16]^data_Packet[8]^data_Packet[0]^temp_data[8]^temp_data[0]
											};
							tx_state <= tx_end;		
						end
						else begin
							tx_wr 		<= 1'b0;
							tx_data_dy 	<= tx_data_dy;
							tx_state 	<= tx_Seventh;												
						end
					end	 			
			tx_end:begin
						tx_wr <= 1'b0;
						if(tx_done == 1'b1)begin
							tx_state <= tx_idle;	
						end
						else begin
							tx_state <= tx_end;												
						end
					end
			default: tx_state <= tx_idle;
		endcase
	end 
end

//tx_data_dy:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	tx_data <= tx_data_dy;
end

endmodule