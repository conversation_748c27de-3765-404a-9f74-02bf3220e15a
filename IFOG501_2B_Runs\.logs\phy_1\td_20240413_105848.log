============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Sat Apr 13 10:58:48 2024

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 16 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1630 instances
RUN-0007 : 395 luts, 972 seqs, 139 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2186 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1652 nets have 2 pins
RUN-1001 : 412 nets have [3 - 5] pins
RUN-1001 : 81 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1628 instances, 395 luts, 972 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7766, tnet num: 2184, tinst num: 1628, tnode num: 10952, tedge num: 13089.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.194215s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (8.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 597836
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1628.
PHY-3001 : End clustering;  0.000007s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 489094, overlap = 20.25
PHY-3002 : Step(2): len = 452355, overlap = 20.25
PHY-3002 : Step(3): len = 415736, overlap = 20.25
PHY-3002 : Step(4): len = 390306, overlap = 20.25
PHY-3002 : Step(5): len = 380104, overlap = 20.25
PHY-3002 : Step(6): len = 370807, overlap = 20.25
PHY-3002 : Step(7): len = 336588, overlap = 18
PHY-3002 : Step(8): len = 304964, overlap = 18
PHY-3002 : Step(9): len = 299881, overlap = 20.25
PHY-3002 : Step(10): len = 295248, overlap = 20.25
PHY-3002 : Step(11): len = 285082, overlap = 20.25
PHY-3002 : Step(12): len = 271827, overlap = 20.25
PHY-3002 : Step(13): len = 267649, overlap = 20.25
PHY-3002 : Step(14): len = 262041, overlap = 20.25
PHY-3002 : Step(15): len = 253399, overlap = 20.25
PHY-3002 : Step(16): len = 247600, overlap = 20.25
PHY-3002 : Step(17): len = 243820, overlap = 20.25
PHY-3002 : Step(18): len = 235274, overlap = 20.25
PHY-3002 : Step(19): len = 228884, overlap = 20.25
PHY-3002 : Step(20): len = 226398, overlap = 20.25
PHY-3002 : Step(21): len = 220612, overlap = 20.25
PHY-3002 : Step(22): len = 212560, overlap = 20.25
PHY-3002 : Step(23): len = 209131, overlap = 20.25
PHY-3002 : Step(24): len = 205104, overlap = 20.25
PHY-3002 : Step(25): len = 197468, overlap = 20.25
PHY-3002 : Step(26): len = 192365, overlap = 20.25
PHY-3002 : Step(27): len = 190199, overlap = 20.25
PHY-3002 : Step(28): len = 182640, overlap = 20.25
PHY-3002 : Step(29): len = 160299, overlap = 20.25
PHY-3002 : Step(30): len = 156041, overlap = 20.25
PHY-3002 : Step(31): len = 153206, overlap = 20.25
PHY-3002 : Step(32): len = 150753, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000196621
PHY-3002 : Step(33): len = 152898, overlap = 13.5
PHY-3002 : Step(34): len = 151951, overlap = 9
PHY-3002 : Step(35): len = 148730, overlap = 15.75
PHY-3002 : Step(36): len = 145621, overlap = 13.5
PHY-3002 : Step(37): len = 143437, overlap = 9
PHY-3002 : Step(38): len = 140094, overlap = 11.25
PHY-3002 : Step(39): len = 139043, overlap = 13.5
PHY-3002 : Step(40): len = 134159, overlap = 11.25
PHY-3002 : Step(41): len = 132141, overlap = 6.75
PHY-3002 : Step(42): len = 129543, overlap = 9
PHY-3002 : Step(43): len = 126858, overlap = 6.75
PHY-3002 : Step(44): len = 124335, overlap = 9
PHY-3002 : Step(45): len = 121366, overlap = 11.25
PHY-3002 : Step(46): len = 118420, overlap = 15.75
PHY-3002 : Step(47): len = 116392, overlap = 6.75
PHY-3002 : Step(48): len = 114745, overlap = 6.75
PHY-3002 : Step(49): len = 111652, overlap = 9
PHY-3002 : Step(50): len = 111209, overlap = 6.75
PHY-3002 : Step(51): len = 109185, overlap = 11.25
PHY-3002 : Step(52): len = 106983, overlap = 15.75
PHY-3002 : Step(53): len = 104734, overlap = 15.75
PHY-3002 : Step(54): len = 104301, overlap = 15.75
PHY-3002 : Step(55): len = 102227, overlap = 9
PHY-3002 : Step(56): len = 101020, overlap = 6.8125
PHY-3002 : Step(57): len = 99155.8, overlap = 6.875
PHY-3002 : Step(58): len = 98394.1, overlap = 6.875
PHY-3002 : Step(59): len = 96036.7, overlap = 9.0625
PHY-3002 : Step(60): len = 95222.9, overlap = 15.8125
PHY-3002 : Step(61): len = 93968.5, overlap = 15.875
PHY-3002 : Step(62): len = 91570.3, overlap = 6.875
PHY-3002 : Step(63): len = 88433.8, overlap = 6.75
PHY-3002 : Step(64): len = 87951.3, overlap = 6.75
PHY-3002 : Step(65): len = 87034.1, overlap = 7.0625
PHY-3002 : Step(66): len = 85607.8, overlap = 9.5625
PHY-3002 : Step(67): len = 84778.7, overlap = 9.875
PHY-3002 : Step(68): len = 83516.5, overlap = 12.125
PHY-3002 : Step(69): len = 82770.8, overlap = 7.875
PHY-3002 : Step(70): len = 81227.8, overlap = 8.125
PHY-3002 : Step(71): len = 79747, overlap = 5.9375
PHY-3002 : Step(72): len = 78354.3, overlap = 8.1875
PHY-3002 : Step(73): len = 77464.9, overlap = 10.5
PHY-3002 : Step(74): len = 76626.4, overlap = 8.25
PHY-3002 : Step(75): len = 76482.5, overlap = 8.3125
PHY-3002 : Step(76): len = 74928.8, overlap = 12.75
PHY-3002 : Step(77): len = 69808.2, overlap = 15.5625
PHY-3002 : Step(78): len = 68739.3, overlap = 11.1562
PHY-3002 : Step(79): len = 68734.1, overlap = 9.25
PHY-3002 : Step(80): len = 68727.1, overlap = 9.25
PHY-3002 : Step(81): len = 68047, overlap = 9.46875
PHY-3002 : Step(82): len = 66993.7, overlap = 13.9688
PHY-3002 : Step(83): len = 66669, overlap = 14.1562
PHY-3002 : Step(84): len = 66458.4, overlap = 9.375
PHY-3002 : Step(85): len = 65537.3, overlap = 7.40625
PHY-3002 : Step(86): len = 64395.5, overlap = 9.59375
PHY-3002 : Step(87): len = 63527.3, overlap = 16.4062
PHY-3002 : Step(88): len = 62849.1, overlap = 16.2188
PHY-3002 : Step(89): len = 62586.3, overlap = 11.5312
PHY-3002 : Step(90): len = 61947.3, overlap = 7.21875
PHY-3002 : Step(91): len = 60588.9, overlap = 11.7188
PHY-3002 : Step(92): len = 59362.5, overlap = 11.7188
PHY-3002 : Step(93): len = 59093.2, overlap = 11.7188
PHY-3002 : Step(94): len = 58132.6, overlap = 13.9688
PHY-3002 : Step(95): len = 57531.4, overlap = 11.7188
PHY-3002 : Step(96): len = 57041.6, overlap = 11.7188
PHY-3002 : Step(97): len = 57047.8, overlap = 11.7188
PHY-3002 : Step(98): len = 56846.7, overlap = 11.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000393241
PHY-3002 : Step(99): len = 56792.6, overlap = 11.7188
PHY-3002 : Step(100): len = 56763.8, overlap = 11.7188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000786482
PHY-3002 : Step(101): len = 56839.3, overlap = 11.7188
PHY-3002 : Step(102): len = 56809.9, overlap = 11.7188
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003849s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.041553s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (75.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(103): len = 60141.2, overlap = 11.4062
PHY-3002 : Step(104): len = 58462.9, overlap = 12.0312
PHY-3002 : Step(105): len = 57999, overlap = 11.875
PHY-3002 : Step(106): len = 57458.1, overlap = 12.8125
PHY-3002 : Step(107): len = 56687.2, overlap = 12.25
PHY-3002 : Step(108): len = 55671.1, overlap = 12.5938
PHY-3002 : Step(109): len = 54597.9, overlap = 12.375
PHY-3002 : Step(110): len = 53594.7, overlap = 13.1875
PHY-3002 : Step(111): len = 52411.8, overlap = 14.8125
PHY-3002 : Step(112): len = 51297.3, overlap = 15.7812
PHY-3002 : Step(113): len = 50827.7, overlap = 15.9375
PHY-3002 : Step(114): len = 49751.7, overlap = 16.375
PHY-3002 : Step(115): len = 49593.8, overlap = 16.5
PHY-3002 : Step(116): len = 49290.7, overlap = 16.6875
PHY-3002 : Step(117): len = 48772.9, overlap = 15.7812
PHY-3002 : Step(118): len = 48421.1, overlap = 15.1875
PHY-3002 : Step(119): len = 48132.3, overlap = 14.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000602338
PHY-3002 : Step(120): len = 47982.9, overlap = 14.7812
PHY-3002 : Step(121): len = 48007.3, overlap = 14.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00120468
PHY-3002 : Step(122): len = 47775.1, overlap = 15.1562
PHY-3002 : Step(123): len = 47791.5, overlap = 15.3438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046064s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (67.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.0738e-05
PHY-3002 : Step(124): len = 48836.3, overlap = 64.25
PHY-3002 : Step(125): len = 49540.4, overlap = 58.75
PHY-3002 : Step(126): len = 49407.1, overlap = 57.5938
PHY-3002 : Step(127): len = 49343.9, overlap = 53.3438
PHY-3002 : Step(128): len = 49759.7, overlap = 52.6562
PHY-3002 : Step(129): len = 49508.8, overlap = 51.9688
PHY-3002 : Step(130): len = 49522, overlap = 50.7188
PHY-3002 : Step(131): len = 49844.4, overlap = 47.9375
PHY-3002 : Step(132): len = 49957, overlap = 46.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000161476
PHY-3002 : Step(133): len = 50178.9, overlap = 42.375
PHY-3002 : Step(134): len = 50355.4, overlap = 42.2188
PHY-3002 : Step(135): len = 50899.9, overlap = 42.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000322952
PHY-3002 : Step(136): len = 51261.3, overlap = 40.5625
PHY-3002 : Step(137): len = 51893.6, overlap = 34.6875
PHY-3002 : Step(138): len = 53374, overlap = 32.75
PHY-3002 : Step(139): len = 53855.6, overlap = 30.0938
PHY-3002 : Step(140): len = 53571.7, overlap = 30.75
PHY-3002 : Step(141): len = 53344, overlap = 30.75
PHY-3002 : Step(142): len = 53140.6, overlap = 27.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7766, tnet num: 2184, tinst num: 1628, tnode num: 10952, tedge num: 13089.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 87.22 peak overflow 2.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2186.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 56064, over cnt = 242(0%), over = 965, worst = 17
PHY-1001 : End global iterations;  0.050917s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (30.7%)

PHY-1001 : Congestion index: top1 = 41.29, top5 = 25.43, top10 = 16.47, top15 = 11.73.
PHY-1001 : End incremental global routing;  0.088962s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (52.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045404s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1589 has valid locations, 4 needs to be replaced
PHY-3001 : design contains 1631 instances, 395 luts, 975 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 53308.6
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7778, tnet num: 2187, tinst num: 1631, tnode num: 10973, tedge num: 13107.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2187 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.203695s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (38.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(143): len = 53403.7, overlap = 2.9375
PHY-3002 : Step(144): len = 53400.2, overlap = 3.125
PHY-3002 : Step(145): len = 53436.6, overlap = 3.125
PHY-3002 : Step(146): len = 53436, overlap = 3.125
PHY-3002 : Step(147): len = 53436, overlap = 3.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2187 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038029s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (82.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000394969
PHY-3002 : Step(148): len = 53441.1, overlap = 28.0938
PHY-3002 : Step(149): len = 53441.1, overlap = 28.0938
PHY-3001 : Final: Len = 53441.1, Over = 28.0938
PHY-3001 : End incremental placement;  0.321163s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (43.8%)

OPT-1001 : Total overflow 87.47 peak overflow 2.78
OPT-1001 : End high-fanout net optimization;  0.480013s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (42.3%)

OPT-1001 : Current memory(MB): used = 214, reserve = 187, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1699/2189.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 56272, over cnt = 242(0%), over = 961, worst = 17
PHY-1002 : len = 64136, over cnt = 145(0%), over = 260, worst = 11
PHY-1002 : len = 66264, over cnt = 33(0%), over = 56, worst = 11
PHY-1002 : len = 67056, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 67336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.063420s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (49.3%)

PHY-1001 : Congestion index: top1 = 36.03, top5 = 25.43, top10 = 18.20, top15 = 13.40.
OPT-1001 : End congestion update;  0.095897s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (48.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2187 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.037976s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (41.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.135592s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (46.1%)

OPT-1001 : Current memory(MB): used = 211, reserve = 184, peak = 214.
OPT-1001 : End physical optimization;  0.802210s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (40.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 395 LUT to BLE ...
SYN-4008 : Packed 395 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 793 remaining SEQ's ...
SYN-4005 : Packed 118 SEQ with LUT/SLICE
SYN-4006 : 113 single LUT's are left
SYN-4006 : 675 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1391 primitive instances ...
PHY-3001 : End packing;  0.037408s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2022 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1492 nets have 2 pins
RUN-1001 : 406 nets have [3 - 5] pins
RUN-1001 : 82 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 829 instances, 782 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53324.2, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6559, tnet num: 2020, tinst num: 829, tnode num: 8868, tedge num: 11478.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.218461s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (57.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.79345e-05
PHY-3002 : Step(150): len = 52638, overlap = 60
PHY-3002 : Step(151): len = 52111.1, overlap = 61.5
PHY-3002 : Step(152): len = 51709.3, overlap = 60.75
PHY-3002 : Step(153): len = 51715.9, overlap = 60.75
PHY-3002 : Step(154): len = 51281.8, overlap = 60.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.5869e-05
PHY-3002 : Step(155): len = 51831.9, overlap = 58.5
PHY-3002 : Step(156): len = 52349.5, overlap = 55.5
PHY-3002 : Step(157): len = 52763.4, overlap = 52.5
PHY-3002 : Step(158): len = 52690.2, overlap = 52.75
PHY-3002 : Step(159): len = 52690.2, overlap = 52.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000111738
PHY-3002 : Step(160): len = 53397.2, overlap = 52
PHY-3002 : Step(161): len = 54408.9, overlap = 48.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.063628s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (49.1%)

PHY-3001 : Trial Legalized: Len = 68230.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.033054s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000641673
PHY-3002 : Step(162): len = 65371.5, overlap = 5.5
PHY-3002 : Step(163): len = 63277.5, overlap = 14.5
PHY-3002 : Step(164): len = 61770.9, overlap = 17
PHY-3002 : Step(165): len = 60548, overlap = 18
PHY-3002 : Step(166): len = 59890.6, overlap = 20.75
PHY-3002 : Step(167): len = 59427.1, overlap = 23.75
PHY-3002 : Step(168): len = 59239.3, overlap = 24.5
PHY-3002 : Step(169): len = 58989.4, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00128335
PHY-3002 : Step(170): len = 59505.1, overlap = 22.75
PHY-3002 : Step(171): len = 59587.3, overlap = 23
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00256669
PHY-3002 : Step(172): len = 59779.7, overlap = 23
PHY-3002 : Step(173): len = 59832.5, overlap = 22.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003942s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64450.5, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003966s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 16 instances has been re-located, deltaX = 4, deltaY = 12, maxDist = 1.
PHY-3001 : Final: Len = 64756.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6559, tnet num: 2020, tinst num: 829, tnode num: 8868, tedge num: 11478.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 81/2022.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 71872, over cnt = 144(0%), over = 209, worst = 5
PHY-1002 : len = 72488, over cnt = 87(0%), over = 109, worst = 4
PHY-1002 : len = 73584, over cnt = 7(0%), over = 9, worst = 3
PHY-1002 : len = 73712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.073522s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (63.8%)

PHY-1001 : Congestion index: top1 = 32.69, top5 = 23.31, top10 = 18.09, top15 = 14.23.
PHY-1001 : End incremental global routing;  0.109488s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (57.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038972s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (40.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.168368s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (46.4%)

OPT-1001 : Current memory(MB): used = 213, reserve = 187, peak = 214.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1799/2022.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 73712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003227s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.69, top5 = 23.31, top10 = 18.09, top15 = 14.23.
OPT-1001 : End congestion update;  0.033917s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (46.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.031877s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (49.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 791 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 829 instances, 782 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64740.2, Over = 0
PHY-3001 : End spreading;  0.003187s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64740.2, Over = 0
PHY-3001 : End incremental legalization;  0.024082s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.099507s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (31.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 192, peak = 218.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032639s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1787/2022.
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 73688, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009418s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.91, top5 = 23.35, top10 = 18.12, top15 = 14.24.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.031927s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (48.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.575231s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (54.3%)

RUN-1003 : finish command "place" in  4.391949s wall, 1.218750s user + 0.265625s system = 1.484375s CPU (33.8%)

RUN-1004 : used memory is 190 MB, reserved memory is 163 MB, peak memory is 218 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |    off     |       off        |        
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 16 thread(s)
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2022 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1492 nets have 2 pins
RUN-1001 : 406 nets have [3 - 5] pins
RUN-1001 : 82 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6559, tnet num: 2020, tinst num: 829, tnode num: 8868, tedge num: 11478.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 16 thread ...
PHY-1002 : len = 70896, over cnt = 143(0%), over = 210, worst = 5
PHY-1002 : len = 71496, over cnt = 88(0%), over = 112, worst = 4
PHY-1002 : len = 72296, over cnt = 38(0%), over = 46, worst = 3
PHY-1002 : len = 72936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.074111s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.19, top10 = 17.93, top15 = 14.07.
PHY-1001 : End global routing;  0.109177s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (14.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 233, reserve = 207, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 502, reserve = 480, peak = 502.
PHY-1001 : End build detailed router design. 2.389993s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (21.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32888, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.790954s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (27.7%)

PHY-1001 : Current memory(MB): used = 534, reserve = 514, peak = 534.
PHY-1001 : End phase 1; 0.795525s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (27.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179528, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 515, peak = 536.
PHY-1001 : End initial routed; 1.075834s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (21.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1796(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.158   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.465948s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (87.2%)

PHY-1001 : Current memory(MB): used = 538, reserve = 516, peak = 538.
PHY-1001 : End phase 2; 1.541865s wall, 0.625000s user + 0.015625s system = 0.640625s CPU (41.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179528, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016491s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (94.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179528, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031509s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (49.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179576, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.018891s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1796(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.158   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.412039s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.219157s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Current memory(MB): used = 552, reserve = 531, peak = 552.
PHY-1001 : End phase 3; 0.946341s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (24.8%)

PHY-1003 : Routed, final wirelength = 179576
PHY-1001 : Current memory(MB): used = 553, reserve = 532, peak = 553.
PHY-1001 : End export database. 0.007462s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  5.886676s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (27.6%)

RUN-1003 : finish command "route" in  6.251978s wall, 1.671875s user + 0.031250s system = 1.703125s CPU (27.2%)

RUN-1004 : used memory is 526 MB, reserved memory is 507 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   15
  #output                  18
  #inout                    1

Utilization Statistics
#lut                      827   out of  19600    4.22%
#reg                     1050   out of  19600    5.36%
#le                      1502
  #lut only               452   out of   1502   30.09%
  #reg only               675   out of   1502   44.94%
  #lut&reg                375   out of   1502   24.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         465
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
  RxTransmit       INPUT        P74        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1502   |613     |214     |1081    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1097   |307     |121     |900     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |19      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |523    |124     |53      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |15      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |138    |15      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |64     |21      |14      |60      |0       |1       |
|    rs422                   |Rs422Output                                      |318    |107     |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |108    |95      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |30     |23      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |26     |20      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |52     |52      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1456  
    #2          2       274   
    #3          3       120   
    #4          4        12   
    #5        5-10       86   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6559, tnet num: 2020, tinst num: 829, tnode num: 8868, tedge num: 11478.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2020 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 16 threads.
BIT-1002 : Init instances completely, inst num: 829
BIT-1002 : Init pips with 16 threads.
BIT-1002 : Init pips completely, net num: 2022, pip num: 14713
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 16 threads.
BIT-1003 : Generate bitstream completely, there are 1282 valid insts, and 38808 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.288228s wall, 11.078125s user + 0.031250s system = 11.109375s CPU (337.9%)

RUN-1004 : used memory is 518 MB, reserved memory is 495 MB, peak memory is 708 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240413_105848.log"
