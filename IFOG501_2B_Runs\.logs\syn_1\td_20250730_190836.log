============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     LJF
   Run Date =   Wed Jul 30 19:08:36 2025

   Run on =     DESKTOP-Q63G60C
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "elaborate -top IFOG501_2B"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : elaborate module IFOG501_2B in ../../Src_al/IFOG501_2B.v(17)
HDL-5007 WARNING: latch inferred for net 'dlycnt[19]' in ../../Src_al/IFOG501_2B.v(74)
HDL-1007 : elaborate module global_clock in ../../al_ip/global_clock.v(24)
HDL-1007 : elaborate module EG_PHY_PLL(FIN="20.000",FBCLK_DIV=48,CLKC0_DIV=8,CLKC3_DIV=16,CLKC4_DIV=16,CLKC0_ENABLE="ENABLE",CLKC3_ENABLE="ENABLE",CLKC4_ENABLE="ENABLE",FEEDBK_MODE="NOCOMP",STDBY_ENABLE="DISABLE",CLKC0_CPHASE=7,CLKC3_CPHASE=15,CLKC4_CPHASE=15,GMC_GAIN=2,ICP_CURRENT=9,KVCO=2,LPF_CAPACITOR=1,LPF_RESISTOR=8,SYNC_ENABLE="DISABLE") in C:/Anlogic/TD5.6.2/arch/eagle_macro.v(930)
HDL-1007 : elaborate module EG_LOGIC_ODDR in C:/Anlogic/TD5.6.2/arch/eagle_macro.v(87)
HDL-1007 : elaborate module DS18B20 in ../../Src_al/DS18B20.v(17)
HDL-1007 : elaborate module UART_Control in ../../Src_al/UART_Control.v(17)
HDL-1007 : elaborate module speed_select_Tx in ../../Src_al/speed_select_Tx.v(17)
HDL-1007 : elaborate module uart_tx in ../../Src_al/uart_tx.v(16)
HDL-1007 : elaborate module Ctrl_Data in ../../Src_al/Ctrl_Data.v(17)
HDL-1007 : elaborate module SignalProcessing(acum_cnt=16,iWID_TRANS=13,wCLOSED=1'b0,iTRANSIT_TIME=182,iAD_VALID_START=90,iFEEDBACK_SCALE=10,iOUTPUT_SCALE=1350,iDELAYED=120,DA_CONSTANT=13330,iTRANSMIT_COFF=600000) in ../../Src_al/SignalProcessing.v(41)
HDL-1007 : elaborate module SignalGenerator(iTRANSIT_TIME=182,iAD_VALID_START=90) in ../../Src_al/SignalGenerator.v(42)
HDL-1007 : elaborate module Demodulation(acum_cnt=16) in ../../Src_al/Demodulation.v(42)
HDL-1007 : port 'doa' remains unconnected for this instance in ../../al_ip/Asys_fifo56X16.v(261)
HDL-1007 : port 'dib' remains unconnected for this instance in ../../al_ip/Asys_fifo56X16.v(261)
HDL-1007 : elaborate module Asys_fifo56X16 in ../../al_ip/Asys_fifo56X16.v(26)
HDL-1007 : elaborate module fifo_cross_domain_addr_process_al_Asys_fifo56X16(ADDR_WIDTH=7) in ../../al_ip/Asys_fifo56X16.v(290)
HDL-1007 : elaborate module ram_infer_Asys_fifo56X16(DATAWIDTH_A=56,ADDRWIDTH_A=7,DATAWIDTH_B=56,REGMODE_B="OUTREG",ADDRWIDTH_B=7) in ../../al_ip/Asys_fifo56X16.v(378)
HDL-1007 : extracting RAM for identifier 'memory' in ../../al_ip/Asys_fifo56X16.v(443)
HDL-5007 WARNING: input port 'dib[55]' is not connected on this instance in ../../al_ip/Asys_fifo56X16.v(266)
HDL-1007 : elaborate module Integration in ../../Src_al/Integration.v(42)
HDL-1007 : elaborate module Modulation(iWID_TRANS=13,DA_CONSTANT=13330,wCLOSED=1'b0) in ../../Src_al/Modulation.v(42)
HDL-5007 WARNING: net 'c_stair[1]' does not have a driver in ../../Src_al/Modulation.v(70)
HDL-1007 : elaborate module Rs422Output(iDELAYED=120) in ../../Src_al/Rs422Output.v(43)
HDL-1007 : elaborate module SquareWaveGenerator(iTRANSMIT_COFF=600000) in ../../Src_al/SquareWaveGenerator.v(16)
HDL-1200 : Current top model is IFOG501_2B
HDL-1100 : Inferred 1 RAMs.
RUN-1002 : start command "export_db IFOG501_2B_elaborate.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "read_adc ../../Constraints/IFOG_11FB.adc"
RUN-1002 : start command "set_pin_assignment  AD_DATA[0]   LOCATION = P19; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[0]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[0]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[0]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[10]   LOCATION = P3; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[10]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[10]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[10]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[11]   LOCATION = P2; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[11]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[11]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[11]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[1]   LOCATION = P18; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[1]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[1]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[1]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[2]   LOCATION = P17; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[2]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[2]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[2]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[3]   LOCATION = P16; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[3]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[3]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[3]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[4]   LOCATION = P14; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[4]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[4]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[4]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[5]   LOCATION = P12; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[5]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[5]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[5]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[6]   LOCATION = P11; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[6]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[6]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[6]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[7]   LOCATION = P10; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[7]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[7]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[7]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[8]   LOCATION = P5; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[8]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[8]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[8]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[9]   LOCATION = P4; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[9]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[9]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[9]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[0]   LOCATION = P60; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[0]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[0]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[0]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[0]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[0]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[10]   LOCATION = P49; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[10]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[10]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[10]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[10]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[10]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[11]   LOCATION = P48; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[11]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[11]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[11]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[11]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[11]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[12]   LOCATION = P47; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[12]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[12]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[12]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[12]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[12]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[13]   LOCATION = P45; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[13]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[13]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[13]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[13]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[13]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[1]   LOCATION = P59; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[1]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[1]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[1]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[1]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[1]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[2]   LOCATION = P57; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[2]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[2]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[2]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[2]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[2]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[3]   LOCATION = P61; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[3]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[3]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[3]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[3]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[3]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[4]   LOCATION = P62; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[4]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[4]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[4]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[4]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[4]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[5]   LOCATION = P63; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[5]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[5]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[5]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[5]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[5]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[6]   LOCATION = P55; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[6]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[6]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[6]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[6]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[6]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[7]   LOCATION = P52; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[7]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[7]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[7]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[7]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[7]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[8]   LOCATION = P51; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[8]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[8]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[8]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[8]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[8]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[9]   LOCATION = P50; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[9]   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[9]   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[9]   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[9]   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[9]   PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  RXD   LOCATION = P77; "
RUN-1002 : start command "set_pin_assignment  RXD   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  RXD   PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  RxTransmit   LOCATION = P74; "
RUN-1002 : start command "set_pin_assignment  RxTransmit   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  RxTransmit   PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  TXD   LOCATION = P76; "
RUN-1002 : start command "set_pin_assignment  TXD   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  TXD   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  TXD   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  TxTransmit   LOCATION = P79; "
RUN-1002 : start command "set_pin_assignment  TxTransmit   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  TxTransmit   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  TxTransmit   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  clk_ADo   LOCATION = P13; "
RUN-1002 : start command "set_pin_assignment  clk_ADo   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  clk_ADo   DRIVESTRENGTH = 16; "
RUN-1002 : start command "set_pin_assignment  clk_ADo   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  clk_DA   LOCATION = P54; "
RUN-1002 : start command "set_pin_assignment  clk_DA   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  clk_DA   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  clk_DA   PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  clk_DA   SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  clk_in   LOCATION = P34; "
RUN-1002 : start command "set_pin_assignment  clk_in   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  clk_in   PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  dq   LOCATION = P87; "
RUN-1002 : start command "set_pin_assignment  dq   IOSTANDARD = LVCMOS33; "
RUN-1002 : start command "set_pin_assignment  dq   DRIVESTRENGTH = 8; "
RUN-1002 : start command "set_pin_assignment  dq   PULLTYPE = PULLUP; "
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO vref setups legality check.
RUN-1002 : start command "optimize_rtl"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-8418 ERROR: License expired!
RUN-1002 : start command "backup_run_log run.log ../.logs/syn_1/td_20250730_190836.log"
