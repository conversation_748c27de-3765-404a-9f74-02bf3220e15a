`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	Integration
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	
// Revision 1.01 - File Created
// Additional Comments: 
//Time sequence diagram
//   ___
//  |   |
//__|   |_________________________________________//clk_DA
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate
//0………………………………………………………………………………………………………  242 245
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module Modulation
#(
	parameter iWID_TRANS=9,	   //Set the number of transfers
	parameter iWID_IN=56,
	parameter iWID_OUT=14,
	parameter DA_CONSTANT=11950,
	parameter iFEEDBACK_SCALE=10,
	parameter wCLOSED=1'b1 
)
(
	input						rst_n,
	input						clk, //80MHz reference clock
	input						polarity,  
	input						modulate,  //modulate调变
	input  signed[iWID_IN-1:0]	din,  //data in
	output reg  [iWID_OUT-1:0]	dout  //data out
);

localparam pi_1_2=14'd4095;//d5460;//完整周期为2的14次方，1/2Π周期为4096
localparam c_step=14'd3500;
//////////////////////////////////////////////////
reg [6:0] 			mudu_dy;	//modulate Signal delay register
reg [iWID_IN-1:0] 	step;
reg signed[iWID_IN-1:0] 	stair;
reg [iWID_OUT-1:0] 	stair_dy0;
reg [iWID_OUT-1:0] 	stair_dy1;
reg [iWID_OUT-1:0]	square;
reg [iWID_OUT-1:0]	square_dy;
reg [iWID_OUT-1:0] 	c_stair;
reg [iWID_OUT-1:0] 	DA_dout;
reg [iWID_OUT-1:0] 	dout_reg; 
reg [iWID_OUT-1:0] 	dout_DY; 
reg [27:0] 			dout_mult; 
reg [27:0] 			DADY_dout; 

//modulate:Delay two beats to generate rising edge signal
always@(posedge clk)begin
	mudu_dy<={mudu_dy[5:0],modulate};	
end

always@(posedge clk)begin
	if(wCLOSED)
		dout_reg<=square_dy+stair_dy0;		
	else 
		dout_reg<=square_dy+c_stair;	
end

//dout_reg:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	dout_DY <= dout_reg;
end

//generate square
always@(posedge clk)begin
	if(mudu_dy[1:0] == 2'b01)//modulate Rising edge
		if(polarity)
			square<=pi_1_2;
		else          
			square<=0;
	else 
		square<=square;
end

//square_DY:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	square_dy <= square;
end

//generate stair
always@(posedge clk)begin
	if(mudu_dy[1:0] == 2'b01)//modulate Rising edge
		stair<=stair+din;
	else 
		stair<=stair;
end	

//stair:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	stair_dy0<=stair[iFEEDBACK_SCALE+iWID_OUT-1:iFEEDBACK_SCALE];
end

//generate constant stair for reguation of halfware voltage
always@(posedge clk)begin
	if(mudu_dy[1:0] == 2'b01)//modulate Rising edge
		c_stair<=c_stair+c_step;
	else 
		c_stair<=c_stair;
end

always@(posedge clk)begin
	if(mudu_dy[4:3] == 2'b01)//modulate Rising edge
		dout_mult<=dout_DY*DA_CONSTANT;
	else 
		dout_mult<=dout_mult;
end

//dout_mult:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	DADY_dout<=dout_mult;
end

always@(posedge clk)begin
	if(mudu_dy[6:5] == 2'b01)//modulate Rising edge
		DA_dout<=DADY_dout[27:14];
	else 
		DA_dout<=DA_dout;
end

//DA_dout:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	dout<=DA_dout;
end

endmodule